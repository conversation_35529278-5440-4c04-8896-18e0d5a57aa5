# Apple App Store Connect API - Complete Endpoint Specifications

## 🔗 **COMPLETE API ENDPOINT DOCUMENTATION:**

### **1. Apps Endpoint** ✅
- **URL**: `GET https://api.appstoreconnect.apple.com/v1/apps`
- **Parameters**:
  - `limit=5` (number of apps to return)
  - `fields[apps]=name,bundleId,sku,primaryLocale` (specific fields)
- **Headers**:
  - `Authorization: Bearer {JWT_TOKEN}`
  - `Content-Type: application/json`
  - `Accept: application/json`
- **Response**: 200 OK, 4 apps found
- **Sample Response**:
```json
{
  "data": [{
    "type": "apps",
    "id": "1502348192",
    "attributes": {
      "name": "Gaudi [D]",
      "bundleId": "com.playamedia.gaudi.debug"
    }
  }]
}
```

### **2. Subscription Groups Endpoint** ✅
- **URL**: `GET https://api.appstoreconnect.apple.com/v1/apps/{APP_ID}/subscriptionGroups`
- **Example**: `GET https://api.appstoreconnect.apple.com/v1/apps/1502348192/subscriptionGroups`
- **Parameters**:
  - `limit=5`
  - `fields[subscriptionGroups]=referenceName`
- **Headers**: Same as above
- **Response**: 200 OK, 1 group "Membership" (ID: 20608680)
- **Sample Response**:
```json
{
  "data": [{
    "type": "subscriptionGroups",
    "id": "20608680",
    "attributes": {
      "referenceName": "Membership"
    }
  }]
}
```

### **3. Subscriptions Endpoint** ✅
- **URL**: `GET https://api.appstoreconnect.apple.com/v1/subscriptionGroups/{GROUP_ID}/subscriptions`
- **Example**: `GET https://api.appstoreconnect.apple.com/v1/subscriptionGroups/20608680/subscriptions`
- **Parameters**:
  - `limit=5`
  - `fields[subscriptions]=name,productId,familySharable,state,subscriptionPeriod,reviewNote,groupLevel`
- **Headers**: Same as above
- **Response**: 200 OK, 5 subscriptions found
- **Sample Response**:
```json
{
  "data": [{
    "type": "subscriptions",
    "id": "1503114882",
    "attributes": {
      "name": "1 Month Premium Membership",
      "productId": "com.playamedia.gaudi.debug.membership.premium.1month",
      "familySharable": false
    }
  }]
}
```### **4. Price Points Endpoint** ✅
- **URL**: `GET https://api.appstoreconnect.apple.com/v1/subscriptions/{SUBSCRIPTION_ID}/pricePoints`
- **Example**: `GET https://api.appstoreconnect.apple.com/v1/subscriptions/1503114882/pricePoints`
- **Parameters**:
  - `limit=50` (default) or custom limit
  - `include=territory` (include territory data)
  - `fields[subscriptionPricePoints]=customerPrice,proceeds,proceedsYear2`
  - `fields[territories]=currency`
  - `cursor=Mg` (for pagination)
- **Headers**: Same as above
- **Response**: 200 OK, 140,117 total price points available
- **Sample Response**:
```json
{
  "data": [{
    "type": "subscriptionPricePoints",
    "id": "eyJzIjoiMTUwMzExNDg4MiIsInQiOiJBRkciLCJwIjoiMTAwMDEifQ",
    "attributes": {
      "customerPrice": "0.29",
      "proceeds": "0.21",
      "proceedsYear2": "0.25"
    },
    "relationships": {
      "territory": {
        "data": {
          "type": "territories",
          "id": "AFG"
        }
      }
    }
  }],
  "included": [{
    "type": "territories",
    "id": "AFG",
    "attributes": {
      "currency": "USD"
    }
  }],
  "meta": {
    "paging": {
      "total": 140117,
      "nextCursor": "Mg",
      "limit": 50
    }
  }
}
```

## 🔐 **JWT AUTHENTICATION DETAILS:**
- **Algorithm**: ES256
- **Required Claims**:
  - `iss`: "8d28e2ea-bda8-48ce-ba39-3a7437716759" (Your Issuer ID)
  - `iat`: Current timestamp
  - `exp`: Current timestamp + 1200 (20 minutes)
  - `aud`: "appstoreconnect-v1"
- **Header**:
  - `alg`: "ES256"
  - `kid`: "K537536885" (Your Key ID)
  - `typ`: "JWT"

## 📊 **SAMPLE RESPONSE DATA:**
- **Customer Prices**: $0.29, $0.39, $0.49... up to $4.00+
- **Developer Proceeds Year 1**: $0.21, $0.28, $0.35... up to $2.80+
- **Developer Proceeds Year 2**: $0.25, $0.33, $0.42... up to $3.40+
- **Territory**: AFG (Afghanistan), Currency: USD
- **Pagination**: 140,117 total records with cursor-based pagination

## 🔄 **API FLOW:**
1. **Apps** → Get all apps in your account
2. **Subscription Groups** → Get groups for a specific app
3. **Subscriptions** → Get subscriptions in a specific group
4. **Price Points** → Get pricing data for a specific subscription

All endpoints are now fully documented and working with your API credentials!