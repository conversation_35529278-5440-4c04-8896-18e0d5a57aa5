import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api/appstore': {
        target: 'https://api.appstoreconnect.apple.com/v1',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/appstore/, ''),
        configure: (proxy) => {
          proxy.on('proxyReq', (_proxyReq, req, res) => {
            // Add CORS headers to handle preflight requests
            if (req.method === 'OPTIONS') {
              res.writeHead(200, {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization',
              });
              res.end();
              return;
            }
          });
        }
      }
    }
  }
})
