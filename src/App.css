/* App Layout */
.app {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.app-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #646cff;
}

.app-header h1 {
  color: #646cff;
  margin-bottom: 0.5rem;
}

.app-header p {
  color: #888;
  font-size: 1.1rem;
}

.app-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Configuration Error Styles */
.config-error {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 8px;
  padding: 2rem;
  text-align: left;
}

.config-error h2 {
  color: #c33;
  margin-top: 0;
}

.config-instructions {
  margin-top: 1.5rem;
}

.config-instructions ol {
  padding-left: 1.5rem;
}

.config-instructions pre {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
  overflow-x: auto;
  font-size: 0.9rem;
}

/* Subscription Selector Styles */
.subscription-selector {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #ddd;
}

.selector-section {
  margin-bottom: 1.5rem;
}

.selector-section:last-child {
  margin-bottom: 0;
}

.selector-section h3 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  color: #333;
}

.subscription-dropdown {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  background: white;
}

.subscription-dropdown:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

/* Territory Controls */
.territory-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.territory-toggle-btn {
  padding: 0.75rem 1rem;
  background: #646cff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  align-self: flex-start;
}

.territory-toggle-btn:hover {
  background: #535bf2;
}

.territory-selector {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
}

.territory-actions {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.territory-action-btn {
  padding: 0.5rem 1rem;
  background: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.territory-action-btn:hover {
  background: #e0e0e0;
}

.territory-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.territory-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.25rem;
}

.territory-checkbox input[type="checkbox"] {
  margin: 0;
}

.territory-label {
  font-size: 0.9rem;
}

/* Fetch Button */
.fetch-btn {
  padding: 1rem 2rem;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: bold;
}

.fetch-btn:hover:not(:disabled) {
  background: #45a049;
}

.fetch-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Selected Subscription Info */
.selected-subscription-info {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
}

.selected-subscription-info h4 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  color: #333;
}

.subscription-details p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

.subscription-details strong {
  color: #333;
}

/* Price Points Display Styles */
.price-points-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #ddd;
}

.price-points-container h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
  text-align: center;
}

.territories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Territory Card */
.territory-card {
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
}

.territory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #ddd;
}

.territory-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.currency-badge {
  background: #646cff;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.price-points-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Price Point Item */
.price-point-item {
  background: white;
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 1rem;
}

.price-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.price-info > div {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.price-info label {
  font-size: 0.8rem;
  color: #666;
  font-weight: bold;
  text-transform: uppercase;
}

.price-info span {
  font-size: 1rem;
  color: #333;
  font-weight: bold;
}

.proceeds-date {
  grid-column: 1 / -1;
}

.subscription-info {
  padding-top: 0.75rem;
  border-top: 1px solid #eee;
}

.subscription-info small {
  color: #666;
  font-size: 0.85rem;
}

/* Loading and Error States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #646cff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
}

.error-container h3 {
  color: #c33;
  margin-top: 0;
}

.error-container p {
  color: #666;
  margin-bottom: 1rem;
}

.retry-btn {
  padding: 0.75rem 1.5rem;
  background: #646cff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.retry-btn:hover {
  background: #535bf2;
}

.no-data-container {
  text-align: center;
  padding: 3rem;
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 1rem;
  }

  .territories-grid {
    grid-template-columns: 1fr;
  }

  .territory-checkboxes {
    grid-template-columns: 1fr;
  }

  .price-info {
    grid-template-columns: 1fr;
  }
}
