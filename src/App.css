/* App Layout */
.app {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.app-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #646cff;
}

.app-header h1 {
  color: #646cff;
  margin-bottom: 0.5rem;
}

.app-header p {
  color: #888;
  font-size: 1.1rem;
}

.app-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Enhanced Filter Layout */
.filters-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.subscription-section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #ddd;
}

/* Configuration Error Styles */
.config-error {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 8px;
  padding: 2rem;
  text-align: left;
}

.config-error h2 {
  color: #c33;
  margin-top: 0;
}

.config-instructions {
  margin-top: 1.5rem;
}

.config-instructions ol {
  padding-left: 1.5rem;
}

.config-instructions pre {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
  overflow-x: auto;
  font-size: 0.9rem;
}

/* Subscription Selector Styles */
.subscription-selector {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #ddd;
}

.selector-section {
  margin-bottom: 1.5rem;
}

.selector-section:last-child {
  margin-bottom: 0;
}

.selector-section h3 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  color: #333;
}

.subscription-dropdown {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  background: white;
}

.subscription-dropdown:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

/* Territory Controls */
.territory-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.territory-toggle-btn {
  padding: 0.75rem 1rem;
  background: #646cff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  align-self: flex-start;
}

.territory-toggle-btn:hover {
  background: #535bf2;
}

.territory-selector {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
}

.territory-actions {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.territory-action-btn {
  padding: 0.5rem 1rem;
  background: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.territory-action-btn:hover {
  background: #e0e0e0;
}

.territory-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.territory-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.25rem;
}

.territory-checkbox input[type="checkbox"] {
  margin: 0;
}

.territory-label {
  font-size: 0.9rem;
}

/* Fetch Button */
.fetch-btn {
  padding: 1rem 2rem;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: bold;
}

.fetch-btn:hover:not(:disabled) {
  background: #45a049;
}

.fetch-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Selected Subscription Info */
.selected-subscription-info {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
}

.selected-subscription-info h4 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  color: #333;
}

.subscription-details p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

.subscription-details strong {
  color: #333;
}

/* Price Points Display Styles */
.price-points-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #ddd;
}

.price-points-container h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
  text-align: center;
}

.territories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Territory Card */
.territory-card {
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
}

.territory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #ddd;
}

.territory-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.currency-badge {
  background: #646cff;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.price-points-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Price Point Item */
.price-point-item {
  background: white;
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 1rem;
}

.price-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.price-info > div {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.price-info label {
  font-size: 0.8rem;
  color: #666;
  font-weight: bold;
  text-transform: uppercase;
}

.price-info span {
  font-size: 1rem;
  color: #333;
  font-weight: bold;
}

.proceeds-date {
  grid-column: 1 / -1;
}

.subscription-info {
  padding-top: 0.75rem;
  border-top: 1px solid #eee;
}

.subscription-info small {
  color: #666;
  font-size: 0.85rem;
}

/* Loading and Error States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #646cff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
}

.error-container h3 {
  color: #c33;
  margin-top: 0;
}

.error-container p {
  color: #666;
  margin-bottom: 1rem;
}

.retry-btn {
  padding: 0.75rem 1.5rem;
  background: #646cff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.retry-btn:hover {
  background: #535bf2;
}

.no-data-container {
  text-align: center;
  padding: 3rem;
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 1rem;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .territories-grid {
    grid-template-columns: 1fr;
  }

  .territory-checkboxes {
    grid-template-columns: 1fr;
  }

  .price-info {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1024px) {
  .filters-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

/* Filter Component Styles */
.app-selector,
.subscription-group-selector,
.territory-selector {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 16px;
  background: white;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.filter-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.selection-summary {
  font-size: 14px;
}

.selected-count {
  color: #059669;
  font-weight: 500;
}

.no-selection {
  color: #6b7280;
}

.filter-controls {
  margin-bottom: 12px;
}

.select-all-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.select-all-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.select-all-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.select-all-btn.selected {
  background: #dc2626;
  color: white;
  border-color: #dc2626;
}

.select-all-btn.partial {
  background: #f59e0b;
  color: white;
  border-color: #f59e0b;
}

.app-list,
.group-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.loading-state,
.empty-state {
  padding: 24px;
  text-align: center;
  color: #6b7280;
  font-style: italic;
}

.app-item,
.group-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s;
}

.app-item:last-child,
.group-item:last-child {
  border-bottom: none;
}

.app-item:hover,
.group-item:hover {
  background: #f9fafb;
}

.app-item input[type="checkbox"],
.group-item input[type="checkbox"] {
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.app-info,
.group-info {
  flex: 1;
  min-width: 0;
}

.app-name,
.group-name {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.app-bundle-id {
  font-size: 13px;
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  margin-bottom: 2px;
}

.app-sku,
.group-id {
  font-size: 12px;
  color: #9ca3af;
}

/* Subscription Group Specific Styles */
.filter-context {
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  color: #0369a1;
}

.app-section {
  border-bottom: 1px solid #e5e7eb;
}

.app-section:last-child {
  border-bottom: none;
}

.app-header {
  padding: 12px 16px;
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
}

/* Territory Selector Specific Styles */
.territory-selector {
  position: relative;
}

.territory-toggle {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s;
}

.territory-toggle:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.territory-toggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toggle-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.selection-count {
  font-size: 13px;
  color: #6b7280;
}

.arrow {
  transition: transform 0.2s;
}

.arrow.expanded {
  transform: rotate(180deg);
}

.territory-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 10;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  margin-top: 4px;
  max-height: 400px;
  overflow: hidden;
}

.territory-controls {
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  gap: 8px;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 13px;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.territory-list {
  max-height: 300px;
  overflow-y: auto;
}

.territory-group {
  border-bottom: 1px solid #f3f4f6;
}

.territory-group:last-child {
  border-bottom: none;
}

.group-header {
  padding: 8px 12px;
  background: #f9fafb;
  font-weight: 600;
  font-size: 12px;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #e5e7eb;
}

.territory-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.territory-item:hover {
  background: #f3f4f6;
}

.territory-item input {
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.territory-info {
  flex: 1;
  min-width: 0;
}

.territory-name {
  display: block;
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
}

.territory-details {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.no-results {
  padding: 16px;
  text-align: center;
  color: #6b7280;
  font-style: italic;
}

/* Subscriptions Table Styles */
.subscriptions-table-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #ddd;
}

.subscriptions-table-container h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.table-wrapper {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.subscriptions-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.subscriptions-table th {
  background: #f9fafb;
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  white-space: nowrap;
}

.subscriptions-table td {
  padding: 12px 8px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
}

.subscription-row:hover {
  background: #f9fafb;
}

.subscription-name {
  font-weight: 500;
  color: #1f2937;
  max-width: 200px;
}

.app-name,
.group-name {
  color: #6b7280;
  font-size: 13px;
}

.product-id code {
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: #374151;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.period {
  text-transform: capitalize;
  color: #6b7280;
}

.state-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.state-badge.ready_to_submit {
  background: #dbeafe;
  color: #1e40af;
}

.state-badge.approved {
  background: #dcfce7;
  color: #166534;
}

.state-badge.pending_review {
  background: #fef3c7;
  color: #92400e;
}

.state-badge.rejected {
  background: #fee2e2;
  color: #dc2626;
}

.family-sharing {
  color: #6b7280;
}

.prices {
  min-width: 120px;
}

/* Currency Toggle Styles */
.prices-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.currency-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 6px;
  padding: 2px;
  gap: 2px;
}

.currency-btn {
  padding: 4px 8px;
  border: none;
  background: transparent;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.currency-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.currency-btn.active {
  background: #3b82f6;
  color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Price Display Styles */
.price-value {
  font-weight: 600;
  color: #059669;
  font-size: 14px;
}

.price-summary {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.price-item {
  font-weight: 500;
  color: #059669;
  font-size: 13px;
}

.more-prices {
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.loading-text {
  color: #6b7280;
  font-style: italic;
}

.error-text {
  color: #dc2626;
  font-weight: 500;
}

.no-prices {
  color: #9ca3af;
}

.expand-btn {
  padding: 6px 12px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #374151;
  transition: all 0.2s;
  white-space: nowrap;
}

.expand-btn:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.expand-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.expanded-row td {
  padding: 0;
  background: #f9fafb;
}

.subscription-details {
  padding: 20px;
  border-top: 1px solid #e5e7eb;
}

.details-section {
  margin-bottom: 20px;
}

.details-section h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item strong {
  color: #374151;
  font-size: 13px;
}

.detail-item span {
  color: #6b7280;
  font-size: 14px;
}

.loading-section {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  justify-content: center;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-section {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  justify-content: center;
}

.error-message {
  color: #dc2626;
}

.retry-btn,
.load-prices-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: background 0.2s;
}

.retry-btn:hover,
.load-prices-btn:hover {
  background: #2563eb;
}

.prices-section h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.prices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.price-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
}

.territory-info {
  margin-bottom: 8px;
}

.territory-info strong {
  color: #1f2937;
  font-size: 14px;
}

.price-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.customer-price,
.proceeds {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.customer-price label,
.proceeds label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.customer-price span {
  font-weight: 600;
  color: #059669;
}

.proceeds span {
  font-weight: 500;
  color: #374151;
}

.no-prices-section {
  text-align: center;
  padding: 20px;
}

.no-prices-section p {
  color: #6b7280;
  margin-bottom: 12px;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
