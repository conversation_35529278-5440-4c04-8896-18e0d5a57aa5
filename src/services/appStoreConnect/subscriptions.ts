import { AppStoreConnectClient } from './client';
import { COMMON_TERRITORIES } from './types';
import type {
  SubscriptionPricePointsResponse,
  SubscriptionsResponse,
  ProcessedPricePoint,
  PricePointsByCountry,
  PricePointsQueryParams,
  Territory,
  Subscription,
  TerritoryCode
} from './types';

export class SubscriptionService {
  private client: AppStoreConnectClient;

  constructor(client: AppStoreConnectClient) {
    this.client = client;
  }

  /**
   * Get all apps for the authenticated account
   */
  public async getApps(limit: number = 50): Promise<any[]> {
    const params = {
      limit,
      'fields[apps]': 'name,bundleId,sku,primaryLocale'
    };

    const response = await this.client.get<any>('/apps', params);
    return response.data;
  }

  /**
   * Get subscription groups for a specific app
   */
  public async getSubscriptionGroups(appId: string, limit: number = 50): Promise<any[]> {
    const params = {
      limit,
      'fields[subscriptionGroups]': 'referenceName'
    };

    const response = await this.client.get<any>(`/apps/${appId}/subscriptionGroups`, params);
    return response.data;
  }

  /**
   * Get all subscriptions for a specific subscription group
   */
  public async getSubscriptions(subscriptionGroupId: string, limit: number = 50): Promise<Subscription[]> {
    const params = {
      limit,
      'fields[subscriptions]': 'name,productId,familySharable,state,subscriptionPeriod,reviewNote,groupLevel'
    };

    const response = await this.client.get<SubscriptionsResponse>(`/subscriptionGroups/${subscriptionGroupId}/subscriptions`, params);
    return response.data;
  }

  /**
   * Get all subscriptions across all apps and groups
   */
  public async getAllSubscriptions(): Promise<{
    apps: any[],
    subscriptions: Array<Subscription & { appName: string, groupName: string }>
  }> {
    const apps = await this.getApps();
    const allSubscriptions: Array<Subscription & { appName: string, groupName: string }> = [];

    for (const app of apps) {
      try {
        const groups = await this.getSubscriptionGroups(app.id);

        for (const group of groups) {
          try {
            const subscriptions = await this.getSubscriptions(group.id);

            // Add app and group context to each subscription
            const enrichedSubscriptions = subscriptions.map(sub => ({
              ...sub,
              appName: app.attributes?.name || app.id,
              groupName: group.attributes?.referenceName || group.id
            }));

            allSubscriptions.push(...enrichedSubscriptions);
          } catch (error) {
            console.warn(`Failed to get subscriptions for group ${group.id}:`, error);
          }
        }
      } catch (error) {
        console.warn(`Failed to get subscription groups for app ${app.id}:`, error);
      }
    }

    return { apps, subscriptions: allSubscriptions };
  }

  /**
   * Get price points for a specific subscription
   */
  public async getSubscriptionPricePoints(
    subscriptionId: string,
    options: {
      territories?: TerritoryCode[];
      limit?: number;
      includeAllData?: boolean;
    } = {}
  ): Promise<SubscriptionPricePointsResponse> {
    const params: PricePointsQueryParams = {
      limit: options.limit || 200,
      'fields[subscriptionPricePoints]': 'customerPrice,proceeds',
      'fields[territories]': 'currency',

      include: 'territory'
    };

    // Filter by specific territories if provided
    if (options.territories && options.territories.length > 0) {
      params['filter[territory]'] = options.territories.map(code => COMMON_TERRITORIES[code]);
    }

    const endpoint = `/subscriptions/${subscriptionId}/pricePoints`;
    return await this.client.get<SubscriptionPricePointsResponse>(endpoint, params);
  }

  /**
   * Get all price points for a subscription with pagination handling
   */
  public async getAllSubscriptionPricePoints(
    subscriptionId: string,
    territories?: TerritoryCode[]
  ): Promise<SubscriptionPricePointsResponse> {
    let allPricePoints: any[] = [];
    let allIncluded: any[] = [];
    let nextUrl: string | undefined;
    let isFirstRequest = true;

    do {
      let response: SubscriptionPricePointsResponse;
      
      if (isFirstRequest) {
        response = await this.getSubscriptionPricePoints(subscriptionId, {
          territories,
          limit: 200
        });
        isFirstRequest = false;
      } else {
        // Extract the path from the next URL
        const url = new URL(nextUrl!);
        const path = url.pathname + url.search;
        response = await this.client.get<SubscriptionPricePointsResponse>(path);
      }

      allPricePoints = allPricePoints.concat(response.data);
      if (response.included) {
        allIncluded = allIncluded.concat(response.included);
      }

      nextUrl = response.links.next;
    } while (nextUrl);

    return {
      data: allPricePoints,
      included: allIncluded,
      links: { self: '' },
      meta: {
        paging: {
          total: allPricePoints.length,
          limit: allPricePoints.length
        }
      }
    };
  }

  /**
   * Process raw price points response into a more usable format
   */
  public processePricePoints(response: SubscriptionPricePointsResponse): ProcessedPricePoint[] {
    const territories = new Map<string, Territory>();
    const subscriptions = new Map<string, Subscription>();

    // Build lookup maps from included data
    if (response.included) {
      response.included.forEach(item => {
        if (item.type === 'territories') {
          territories.set(item.id, item as Territory);
        } else if (item.type === 'subscriptions') {
          subscriptions.set(item.id, item as Subscription);
        }
      });
    }

    return response.data.map(pricePoint => {
      const territory = territories.get(pricePoint.relationships.territory.data.id);
      const subscription = subscriptions.get(pricePoint.relationships.subscription.data.id);

      return {
        id: pricePoint.id,
        customerPrice: pricePoint.attributes.customerPrice,
        proceeds: pricePoint.attributes.proceeds,
        proceedsDate: pricePoint.attributes.proceedsDate,
        territory: {
          id: pricePoint.relationships.territory.data.id,
          currency: territory?.attributes.currency || 'Unknown'
        },
        subscription: {
          id: pricePoint.relationships.subscription.data.id,
          name: subscription?.attributes.name,
          productId: subscription?.attributes.productId
        }
      };
    });
  }

  /**
   * Group price points by country/territory
   */
  public groupPricePointsByCountry(pricePoints: ProcessedPricePoint[]): PricePointsByCountry {
    const grouped: PricePointsByCountry = {};

    pricePoints.forEach(pricePoint => {
      const territoryId = pricePoint.territory.id;
      
      if (!grouped[territoryId]) {
        grouped[territoryId] = {
          territory: pricePoint.territory,
          pricePoints: []
        };
      }
      
      grouped[territoryId].pricePoints.push(pricePoint);
    });

    return grouped;
  }

  /**
   * Get processed price points for a subscription, grouped by country
   */
  public async getSubscriptionPricePointsByCountry(
    subscriptionId: string,
    territories?: TerritoryCode[]
  ): Promise<PricePointsByCountry> {
    const response = await this.getAllSubscriptionPricePoints(subscriptionId, territories);
    const processedPricePoints = this.processePricePoints(response);
    return this.groupPricePointsByCountry(processedPricePoints);
  }
}
