import axios, { AxiosError } from 'axios';
import type { AxiosInstance, AxiosResponse } from 'axios';
import { AppStoreConnectAuth } from './auth';

export interface AppStoreConnectError {
  status: string;
  code: string;
  title: string;
  detail: string;
}

export interface AppStoreConnectErrorResponse {
  errors: AppStoreConnectError[];
}

export class AppStoreConnectAPIError extends Error {
  public status: number;
  public errors: AppStoreConnectError[];

  constructor(status: number, errors: AppStoreConnectError[]) {
    const message = errors.map(e => `${e.title}: ${e.detail}`).join('; ');
    super(message);
    this.name = 'AppStoreConnectAPIError';
    this.status = status;
    this.errors = errors;
  }
}

export class AppStoreConnectClient {
  private auth: AppStoreConnectAuth;
  private client: AxiosInstance;
  private baseURL = 'https://api.appstoreconnect.apple.com/v1';

  constructor(auth: AppStoreConnectAuth) {
    this.auth = auth;
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000, // 30 seconds timeout
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor to add authentication
    this.client.interceptors.request.use(
      async (config) => {
        config.headers.Authorization = await this.auth.getAuthorizationHeader();
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.client.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        if (error.response?.status === 401) {
          // Clear cached token and retry once
          this.auth.clearToken();
          
          if (!error.config?.__isRetry) {
            const config = { ...error.config, __isRetry: true };
            if (config.headers) {
              config.headers['Authorization'] = await this.auth.getAuthorizationHeader();
            }
            return this.client.request(config);
          }
        }

        // Handle App Store Connect API errors
        if (error.response?.data && typeof error.response.data === 'object' && error.response.data !== null && 'errors' in error.response.data) {
          const errorData = error.response.data as AppStoreConnectErrorResponse;
          throw new AppStoreConnectAPIError(error.response.status, errorData.errors);
        }

        // Handle other HTTP errors
        throw new Error(`HTTP ${error.response?.status}: ${error.message}`);
      }
    );
  }

  /**
   * Make a GET request to the App Store Connect API
   */
  public async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.get(endpoint, { params });
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Make a POST request to the App Store Connect API
   */
  public async post<T>(endpoint: string, data?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.post(endpoint, data);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Make a PATCH request to the App Store Connect API
   */
  public async patch<T>(endpoint: string, data?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.patch(endpoint, data);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Make a DELETE request to the App Store Connect API
   */
  public async delete<T>(endpoint: string): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.delete(endpoint);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  private handleError(error: any): void {
    if (error instanceof AppStoreConnectAPIError) {
      console.error('App Store Connect API Error:', {
        status: error.status,
        errors: error.errors
      });
    } else {
      console.error('Request Error:', error.message);
    }
  }
}

// Extend AxiosRequestConfig to include retry flag
declare module 'axios' {
  interface AxiosRequestConfig {
    __isRetry?: boolean;
  }
}
