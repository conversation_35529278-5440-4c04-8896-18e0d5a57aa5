import axios, { AxiosError } from 'axios';
import type { AxiosInstance, AxiosResponse } from 'axios';
import { AppStoreConnectAuth } from './auth';

export interface AppStoreConnectError {
  status: string;
  code: string;
  title: string;
  detail: string;
}

export interface AppStoreConnectErrorResponse {
  errors: AppStoreConnectError[];
}

export class AppStoreConnectAPIError extends Error {
  public status: number;
  public errors: AppStoreConnectError[];

  constructor(status: number, errors: AppStoreConnectError[]) {
    const message = errors.map(e => `${e.title}: ${e.detail}`).join('; ');
    super(message);
    this.name = 'AppStoreConnectAPIError';
    this.status = status;
    this.errors = errors;
  }
}

export class AppStoreConnectClient {
  private auth: AppStoreConnectAuth;
  private client: AxiosInstance;
  private baseURL = import.meta.env.DEV ? '/api/appstore' : 'https://api.appstoreconnect.apple.com/v1';

  constructor(auth: AppStoreConnectAuth) {
    this.auth = auth;
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000, // 30 seconds timeout
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor to add authentication
    this.client.interceptors.request.use(
      async (config) => {
        config.headers.Authorization = await this.auth.getAuthorizationHeader();
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.client.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        if (error.response?.status === 401) {
          // Clear cached token and retry once
          console.log('401 error detected, clearing token and retrying...');
          this.auth.clearToken();

          if (!error.config?.__isRetry) {
            console.log('Retrying request with new token...');
            const config = {
              ...error.config,
              __isRetry: true,
              headers: {
                ...error.config?.headers,
                'Authorization': await this.auth.getAuthorizationHeader()
              }
            };
            return this.client.request(config);
          } else {
            console.log('Retry already attempted, failing...');
          }
        }

        // Handle App Store Connect API errors
        if (error.response?.data && typeof error.response.data === 'object' && error.response.data !== null && 'errors' in error.response.data) {
          const errorData = error.response.data as AppStoreConnectErrorResponse;
          throw new AppStoreConnectAPIError(error.response.status, errorData.errors);
        }

        // Handle other HTTP errors
        throw new Error(`HTTP ${error.response?.status}: ${error.message}`);
      }
    );
  }

  /**
   * Custom parameter serializer that doesn't encode square brackets
   */
  private serializeParams(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();

    for (const [key, value] of Object.entries(params)) {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    }

    // Convert to string and decode square brackets
    return searchParams.toString().replace(/%5B/g, '[').replace(/%5D/g, ']');
  }

  /**
   * Make a GET request to the App Store Connect API
   */
  public async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    try {
      let url = endpoint;
      if (params && Object.keys(params).length > 0) {
        const queryString = this.serializeParams(params);
        url += (url.includes('?') ? '&' : '?') + queryString;
      }

      const response: AxiosResponse<T> = await this.client.get(url);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Make a POST request to the App Store Connect API
   */
  public async post<T>(endpoint: string, data?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.post(endpoint, data);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Make a PATCH request to the App Store Connect API
   */
  public async patch<T>(endpoint: string, data?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.patch(endpoint, data);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Make a DELETE request to the App Store Connect API
   */
  public async delete<T>(endpoint: string): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.delete(endpoint);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  private handleError(error: any): void {
    if (error instanceof AppStoreConnectAPIError) {
      console.error('App Store Connect API Error:', {
        status: error.status,
        errors: error.errors
      });
    } else {
      console.error('Request Error:', error.message);
    }
  }
}

// Extend AxiosRequestConfig to include retry flag
declare module 'axios' {
  interface AxiosRequestConfig {
    __isRetry?: boolean;
  }
}
