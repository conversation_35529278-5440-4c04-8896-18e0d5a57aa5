// API Parameter Formatting Utilities for Apple App Store Connect API

export type ParameterValue = string | string[] | number | boolean | undefined;

export interface ApiParameterFormatters {
  'filter[territory]': (territories: string[]) => string;
  'fields[apps]': (fields: string[]) => string;
  'fields[subscriptions]': (fields: string[]) => string;
  'fields[subscriptionGroups]': (fields: string[]) => string;
  'fields[subscriptionPricePoints]': (fields: string[]) => string;
  'fields[territories]': (fields: string[]) => string;
  'include': (includes: string[]) => string;
}

/**
 * Utility object for formatting API parameters according to Apple App Store Connect API requirements
 */
export const apiParamFormatters: ApiParameterFormatters = {
  'filter[territory]': (territories: string[]) => {
    if (!Array.isArray(territories) || territories.length === 0) {
      throw new Error('Territory filter must be a non-empty array');
    }
    return territories.join(',');
  },

  'fields[apps]': (fields: string[]) => {
    if (!Array.isArray(fields) || fields.length === 0) {
      throw new Error('App fields must be a non-empty array');
    }
    return fields.join(',');
  },

  'fields[subscriptions]': (fields: string[]) => {
    if (!Array.isArray(fields) || fields.length === 0) {
      throw new Error('Subscription fields must be a non-empty array');
    }
    return fields.join(',');
  },

  'fields[subscriptionGroups]': (fields: string[]) => {
    if (!Array.isArray(fields) || fields.length === 0) {
      throw new Error('Subscription group fields must be a non-empty array');
    }
    return fields.join(',');
  },

  'fields[subscriptionPricePoints]': (fields: string[]) => {
    if (!Array.isArray(fields) || fields.length === 0) {
      throw new Error('Subscription price point fields must be a non-empty array');
    }
    return fields.join(',');
  },

  'fields[territories]': (fields: string[]) => {
    if (!Array.isArray(fields) || fields.length === 0) {
      throw new Error('Territory fields must be a non-empty array');
    }
    return fields.join(',');
  },

  'include': (includes: string[]) => {
    if (!Array.isArray(includes) || includes.length === 0) {
      throw new Error('Include parameter must be a non-empty array');
    }
    return includes.join(',');
  }
};

/**
 * Generic parameter formatter that handles both arrays and strings
 */
export function formatApiParameter(
  key: keyof ApiParameterFormatters,
  value: string | string[]
): string {
  if (typeof value === 'string') {
    return value;
  }
  
  if (Array.isArray(value)) {
    return apiParamFormatters[key](value);
  }
  
  throw new Error(`Invalid parameter value for ${key}: must be string or string array`);
}

/**
 * Validates and formats multiple API parameters
 */
export function formatApiParameters(
  params: Record<string, ParameterValue>
): Record<string, ParameterValue> {
  const formatted: Record<string, ParameterValue> = {};
  
  for (const [key, value] of Object.entries(params)) {
    if (value === undefined || value === null) {
      continue;
    }
    
    if (key in apiParamFormatters) {
      formatted[key] = formatApiParameter(key as keyof ApiParameterFormatters, value as string | string[]);
    } else {
      formatted[key] = value;
    }
  }
  
  return formatted;
}

/**
 * Common field sets for different entity types
 */
export const COMMON_FIELD_SETS = {
  apps: ['name', 'bundleId', 'sku', 'primaryLocale'],
  subscriptionGroups: ['referenceName'],
  subscriptions: ['name', 'productId', 'familySharable', 'state', 'subscriptionPeriod', 'reviewNote', 'groupLevel'],
  subscriptionPricePoints: ['customerPrice', 'proceeds', 'proceedsYear2'],
  territories: ['currency']
} as const;

/**
 * Helper function to build standardized API parameters
 */
export function buildApiParams(options: {
  limit?: number;
  entityType: keyof typeof COMMON_FIELD_SETS;
  include?: string[];
  filters?: Record<string, string[]>;
  customFields?: string[];
}): Record<string, ParameterValue> {
  const { limit = 50, entityType, include, filters, customFields } = options;
  
  const params: Record<string, ParameterValue> = {
    limit
  };
  
  // Add fields parameter
  const fields = customFields || COMMON_FIELD_SETS[entityType];
  const fieldKey = `fields[${entityType}]` as keyof ApiParameterFormatters;
  if (fieldKey in apiParamFormatters) {
    params[fieldKey] = formatApiParameter(fieldKey, fields);
  }
  
  // Add include parameter
  if (include && include.length > 0) {
    params.include = formatApiParameter('include', include);
  }
  
  // Add filter parameters
  if (filters) {
    for (const [filterKey, filterValue] of Object.entries(filters)) {
      const fullFilterKey = `filter[${filterKey}]` as keyof ApiParameterFormatters;
      if (fullFilterKey in apiParamFormatters) {
        params[fullFilterKey] = formatApiParameter(fullFilterKey, filterValue);
      }
    }
  }
  
  return params;
}
