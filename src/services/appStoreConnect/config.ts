import type { AppStoreConnectConfig } from './auth';

/**
 * Configuration management for App Store Connect API
 */
export class ConfigManager {
  private static instance: ConfigManager;
  private config: AppStoreConnectConfig | null = null;

  private constructor() {}

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  /**
   * Load configuration from environment variables
   */
  public loadFromEnv(): AppStoreConnectConfig {
    // For development, we'll use hardcoded values from the .env file
    // In production, these should be loaded from a secure backend
    const keyId = 'K537536885';
    const issuerId = '8d28e2ea-bda8-48ce-ba39-3a7437716759';
    const privateKey = `*****************************************************************************************************************************************************************************************************************************************************************`;

    if (!keyId || !issuerId || !privateKey) {
      throw new Error(
        'Missing required App Store Connect API configuration. ' +
        'Please check the configuration in config.ts file.'
      );
    }

    this.config = {
      keyId: keyId.trim(),
      issuerId: issuerId.trim(),
      privateKey: this.formatPrivateKey(privateKey.trim())
    };

    return this.config;
  }

  /**
   * Set configuration manually (useful for testing or dynamic configuration)
   */
  public setConfig(config: AppStoreConnectConfig): void {
    this.config = {
      ...config,
      privateKey: this.formatPrivateKey(config.privateKey)
    };
  }

  /**
   * Get current configuration
   */
  public getConfig(): AppStoreConnectConfig {
    if (!this.config) {
      return this.loadFromEnv();
    }
    return this.config;
  }

  /**
   * Check if configuration is loaded and valid
   */
  public isConfigured(): boolean {
    try {
      const config = this.getConfig();
      return !!(config.keyId && config.issuerId && config.privateKey);
    } catch (e) {
      console.error('App Store Connect API is not configured:', e);
      return false;
    }
  }

  /**
   * Format private key to ensure proper PEM format
   */
  private formatPrivateKey(privateKey: string): string {
    // Remove any extra whitespace and normalize line endings
    let formatted = privateKey.replace(/\r\n/g, '\n').trim();

    // If the key doesn't have proper headers, add them
    if (!formatted.includes('-----BEGIN PRIVATE KEY-----')) {
      // Remove any existing headers first
      formatted = formatted
        .replace(/-----BEGIN [^-]+-----/g, '')
        .replace(/-----END [^-]+-----/g, '')
        .replace(/\s/g, '');

      // Add proper headers and format
      const keyBody = formatted.match(/.{1,64}/g)?.join('\n') || formatted;
      formatted = `-----BEGIN PRIVATE KEY-----\n${keyBody}\n-----END PRIVATE KEY-----`;
    }

    return formatted;
  }

  /**
   * Validate that the private key is in correct format
   */
  public validatePrivateKey(privateKey: string): boolean {
    const formatted = this.formatPrivateKey(privateKey);
    
    // Basic validation - check for proper PEM structure
    const pemRegex = /^-----BEGIN PRIVATE KEY-----\n[\s\S]+\n-----END PRIVATE KEY-----$/;
    return pemRegex.test(formatted);
  }

  /**
   * Clear cached configuration
   */
  public clearConfig(): void {
    this.config = null;
  }
}

/**
 * Convenience function to get configuration
 */
export function getAppStoreConnectConfig(): AppStoreConnectConfig {
  return ConfigManager.getInstance().getConfig();
}

/**
 * Convenience function to check if properly configured
 */
export function isAppStoreConnectConfigured(): boolean {
  return ConfigManager.getInstance().isConfigured();
}
