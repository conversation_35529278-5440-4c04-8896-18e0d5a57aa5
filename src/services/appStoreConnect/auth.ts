import { SignJWT, importPKCS8 } from 'jose';

export interface AppStoreConnectConfig {
  keyId: string;
  issuerId: string;
  privateKey: string;
}

export interface JWTClaims {
  iss: string;
  iat: number;
  exp: number;
  aud: string;
}

export class AppStoreConnectAuth {
  private config: AppStoreConnectConfig;
  private cachedToken: string | null = null;
  private tokenExpiry: number = 0;

  constructor(config: AppStoreConnectConfig) {
    this.config = config;
    this.validateConfig();
  }

  private validateConfig(): void {
    if (!this.config.keyId) {
      throw new Error('App Store Connect Key ID is required');
    }
    if (!this.config.issuerId) {
      throw new Error('App Store Connect Issuer ID is required');
    }
    if (!this.config.privateKey) {
      throw new Error('App Store Connect Private Key is required');
    }

    // Validate private key format
    if (!this.config.privateKey.includes('-----BEGIN PRIVATE KEY-----')) {
      throw new Error('Private key must be in PEM format with proper headers');
    }
  }

  /**
   * Generate a JWT token for App Store Connect API authentication
   * Tokens are valid for up to 20 minutes as per Apple's requirements
   */
  public async generateToken(): Promise<string> {
    const now = Math.floor(Date.now() / 1000);
    const expiry = now + (20 * 60); // 20 minutes from now

    try {
      // Import the private key
      const privateKey = await importPKCS8(this.config.privateKey, 'ES256');

      // Create and sign the JWT
      const token = await new SignJWT({
        iss: this.config.issuerId,
        iat: now,
        exp: expiry,
        aud: 'appstoreconnect-v1'
      })
        .setProtectedHeader({
          alg: 'ES256',
          kid: this.config.keyId,
          typ: 'JWT'
        })
        .sign(privateKey);

      return token;
    } catch (error) {
      throw new Error(`Failed to generate JWT token: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get a valid JWT token, using cached token if still valid
   * or generating a new one if expired
   */
  public async getValidToken(): Promise<string> {
    const now = Math.floor(Date.now() / 1000);

    // Check if we have a cached token that's still valid (with 5 minute buffer)
    if (this.cachedToken && this.tokenExpiry > (now + 300)) {
      console.log('Using cached JWT token');
      return this.cachedToken;
    }

    // Generate new token
    console.log('Generating new JWT token...');
    this.cachedToken = await this.generateToken();
    this.tokenExpiry = now + (20 * 60); // 20 minutes from now
    console.log('New JWT token generated successfully');

    return this.cachedToken;
  }

  /**
   * Clear cached token to force regeneration on next request
   */
  public clearToken(): void {
    console.log('Clearing cached JWT token due to 401 error');
    this.cachedToken = null;
    this.tokenExpiry = 0;
  }

  /**
   * Get authorization header value for API requests
   */
  public async getAuthorizationHeader(): Promise<string> {
    const token = await this.getValidToken();
    return `Bearer ${token}`;
  }
}

/**
 * Create an AppStoreConnectAuth instance from environment variables
 */
export function createAuthFromEnv(): AppStoreConnectAuth {
  const config: AppStoreConnectConfig = {
    keyId: process.env.APPLE_KEY_ID || '',
    issuerId: process.env.APPLE_ISSUER_ID || '',
    privateKey: process.env.APPLE_PRIVATE_KEY || ''
  };

  return new AppStoreConnectAuth(config);
}
