// Main exports for App Store Connect API integration
export { AppStoreConnectAuth, createAuthFromEnv } from './auth';
export { AppStoreConnectClient, AppStoreConnectAPIError } from './client';
export { SubscriptionService } from './subscriptions';
export { ConfigManager, getAppStoreConnectConfig, isAppStoreConnectConfigured } from './config';
export * from './types';

// Convenience factory function to create a fully configured service
import { AppStoreConnectAuth } from './auth';
import { AppStoreConnectClient } from './client';
import { SubscriptionService } from './subscriptions';
import { getAppStoreConnectConfig } from './config';

/**
 * Create a fully configured App Store Connect service instance
 */
export function createAppStoreConnectService(): {
  auth: AppStoreConnectAuth;
  client: AppStoreConnectClient;
  subscriptions: SubscriptionService;
} {
  const config = getAppStoreConnectConfig();
  const auth = new AppStoreConnectAuth(config);
  const client = new AppStoreConnectClient(auth);
  const subscriptions = new SubscriptionService(client);

  return {
    auth,
    client,
    subscriptions
  };
}

/**
 * Create service from environment variables (convenience function)
 */
export function createServiceFromEnv(): {
  auth: AppStoreConnectAuth;
  client: AppStoreConnectClient;
  subscriptions: SubscriptionService;
} {
  const auth = createAuthFromEnv();
  const client = new AppStoreConnectClient(auth);
  const subscriptions = new SubscriptionService(client);

  return {
    auth,
    client,
    subscriptions
  };
}
