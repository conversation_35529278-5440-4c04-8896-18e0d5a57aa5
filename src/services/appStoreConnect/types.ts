// App Store Connect API Types for Subscription Price Points

export interface Territory {
  type: 'territories';
  id: string;
  attributes: {
    currency: string;
  };
}

export interface SubscriptionPricePoint {
  type: 'subscriptionPricePoints';
  id: string;
  attributes: {
    customerPrice: string;
    proceeds: string;
    proceedsDate?: string;
  };
  relationships: {
    territory: {
      data: {
        type: 'territories';
        id: string;
      };
    };
    subscription: {
      data: {
        type: 'subscriptions';
        id: string;
      };
    };
  };
}

export interface App {
  type: 'apps';
  id: string;
  attributes: {
    name: string;
    bundleId: string;
    sku?: string;
    primaryLocale?: string;
  };
}

export interface SubscriptionGroup {
  type: 'subscriptionGroups';
  id: string;
  attributes: {
    referenceName: string;
  };
}

export interface Subscription {
  type: 'subscriptions';
  id: string;
  attributes: {
    name: string;
    productId: string;
    familySharable: boolean;
    state: 'MISSING_METADATA' | 'READY_TO_SUBMIT' | 'WAITING_FOR_REVIEW' | 'IN_REVIEW' | 'DEVELOPER_ACTION_NEEDED' | 'REJECTED' | 'APPROVED' | 'DEVELOPER_REMOVED_FROM_SALE' | 'REMOVED_FROM_SALE';
    subscriptionPeriod: 'ONE_WEEK' | 'ONE_MONTH' | 'TWO_MONTHS' | 'THREE_MONTHS' | 'SIX_MONTHS' | 'ONE_YEAR';
    reviewNote?: string;
    groupLevel?: number;
  };
}

export interface SubscriptionPricePointsResponse {
  data: SubscriptionPricePoint[];
  included?: (Territory | Subscription)[];
  links: {
    self: string;
    first?: string;
    next?: string;
    prev?: string;
  };
  meta?: {
    paging: {
      total: number;
      limit: number;
    };
  };
}

export interface AppsResponse {
  data: App[];
  included?: any[];
  links: {
    self: string;
    first?: string;
    next?: string;
    prev?: string;
  };
  meta?: {
    paging: {
      total: number;
      limit: number;
    };
  };
}

export interface SubscriptionGroupsResponse {
  data: SubscriptionGroup[];
  included?: any[];
  links: {
    self: string;
    first?: string;
    next?: string;
    prev?: string;
  };
  meta?: {
    paging: {
      total: number;
      limit: number;
    };
  };
}

export interface SubscriptionsResponse {
  data: Subscription[];
  included?: any[];
  links: {
    self: string;
    first?: string;
    next?: string;
    prev?: string;
  };
  meta?: {
    paging: {
      total: number;
      limit: number;
    };
  };
}

// Processed types for easier consumption
export interface ProcessedPricePoint {
  id: string;
  customerPrice: string;
  proceeds: string;
  proceedsDate?: string;
  territory: {
    id: string;
    currency: string;
  };
  subscription: {
    id: string;
    name?: string;
    productId?: string;
  };
}

export interface PricePointsByCountry {
  [territoryId: string]: {
    territory: {
      id: string;
      currency: string;
    };
    pricePoints: ProcessedPricePoint[];
  };
}

// Query parameters for price points endpoint
export interface PricePointsQueryParams {
  'filter[territory]'?: string;
  'fields[subscriptionPricePoints]'?: string;
  'fields[territories]'?: string;
  'fields[subscriptions]'?: string;
  include?: string;
  limit?: number;
}

// Extended territory codes with display names and currencies
export const TERRITORIES = {
  // North America
  US: { code: 'USA', name: 'United States', currency: 'USD' },
  CA: { code: 'CAN', name: 'Canada', currency: 'CAD' },
  MX: { code: 'MEX', name: 'Mexico', currency: 'MXN' },

  // Europe
  GB: { code: 'GBR', name: 'United Kingdom', currency: 'GBP' },
  DE: { code: 'DEU', name: 'Germany', currency: 'EUR' },
  FR: { code: 'FRA', name: 'France', currency: 'EUR' },
  IT: { code: 'ITA', name: 'Italy', currency: 'EUR' },
  ES: { code: 'ESP', name: 'Spain', currency: 'EUR' },
  NL: { code: 'NLD', name: 'Netherlands', currency: 'EUR' },
  SE: { code: 'SWE', name: 'Sweden', currency: 'SEK' },
  NO: { code: 'NOR', name: 'Norway', currency: 'NOK' },
  DK: { code: 'DNK', name: 'Denmark', currency: 'DKK' },
  CH: { code: 'CHE', name: 'Switzerland', currency: 'CHF' },

  // Asia Pacific
  JP: { code: 'JPN', name: 'Japan', currency: 'JPY' },
  CN: { code: 'CHN', name: 'China', currency: 'CNY' },
  KR: { code: 'KOR', name: 'South Korea', currency: 'KRW' },
  IN: { code: 'IND', name: 'India', currency: 'INR' },
  AU: { code: 'AUS', name: 'Australia', currency: 'AUD' },
  NZ: { code: 'NZL', name: 'New Zealand', currency: 'NZD' },
  SG: { code: 'SGP', name: 'Singapore', currency: 'SGD' },
  HK: { code: 'HKG', name: 'Hong Kong', currency: 'HKD' },
  TW: { code: 'TWN', name: 'Taiwan', currency: 'TWD' },

  // Other Major Markets
  BR: { code: 'BRA', name: 'Brazil', currency: 'BRL' },
  RU: { code: 'RUS', name: 'Russia', currency: 'RUB' },
  TR: { code: 'TUR', name: 'Turkey', currency: 'TRY' },
  ZA: { code: 'ZAF', name: 'South Africa', currency: 'ZAR' },
  IL: { code: 'ISR', name: 'Israel', currency: 'ILS' },
  AE: { code: 'ARE', name: 'United Arab Emirates', currency: 'AED' },
  SA: { code: 'SAU', name: 'Saudi Arabia', currency: 'SAR' }
} as const;

// Backward compatibility
export const COMMON_TERRITORIES = Object.fromEntries(
  Object.entries(TERRITORIES).map(([key, value]) => [key, value.code])
) as Record<keyof typeof TERRITORIES, string>;

export type TerritoryCode = keyof typeof TERRITORIES;

// Territory display information
export interface TerritoryInfo {
  code: string;
  name: string;
  currency: string;
}

// Enhanced subscription with app and group context
export interface EnrichedSubscription extends Subscription {
  appName: string;
  groupName: string;
  appId: string;
  groupId: string;
}
