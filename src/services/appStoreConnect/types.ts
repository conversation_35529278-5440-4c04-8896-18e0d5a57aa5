// App Store Connect API Types for Subscription Price Points

export interface Territory {
  type: 'territories';
  id: string;
  attributes: {
    currency: string;
  };
}

export interface SubscriptionPricePoint {
  type: 'subscriptionPricePoints';
  id: string;
  attributes: {
    customerPrice: string;
    proceeds: string;
    proceedsDate?: string;
  };
  relationships: {
    territory: {
      data: {
        type: 'territories';
        id: string;
      };
    };
    subscription: {
      data: {
        type: 'subscriptions';
        id: string;
      };
    };
  };
}

export interface App {
  type: 'apps';
  id: string;
  attributes: {
    name: string;
    bundleId: string;
    sku?: string;
    primaryLocale?: string;
  };
}

export interface SubscriptionGroup {
  type: 'subscriptionGroups';
  id: string;
  attributes: {
    referenceName: string;
  };
}

export interface Subscription {
  type: 'subscriptions';
  id: string;
  attributes: {
    name: string;
    productId: string;
    familySharable: boolean;
    state: 'MISSING_METADATA' | 'READY_TO_SUBMIT' | 'WAITING_FOR_REVIEW' | 'IN_REVIEW' | 'DEVELOPER_ACTION_NEEDED' | 'REJECTED' | 'APPROVED' | 'DEVELOPER_REMOVED_FROM_SALE' | 'REMOVED_FROM_SALE';
    subscriptionPeriod: 'ONE_WEEK' | 'ONE_MONTH' | 'TWO_MONTHS' | 'THREE_MONTHS' | 'SIX_MONTHS' | 'ONE_YEAR';
    reviewNote?: string;
    groupLevel?: number;
  };
}

export interface SubscriptionPricePointsResponse {
  data: SubscriptionPricePoint[];
  included?: (Territory | Subscription)[];
  links: {
    self: string;
    first?: string;
    next?: string;
    prev?: string;
  };
  meta?: {
    paging: {
      total: number;
      limit: number;
    };
  };
}

export interface AppsResponse {
  data: App[];
  included?: any[];
  links: {
    self: string;
    first?: string;
    next?: string;
    prev?: string;
  };
  meta?: {
    paging: {
      total: number;
      limit: number;
    };
  };
}

export interface SubscriptionGroupsResponse {
  data: SubscriptionGroup[];
  included?: any[];
  links: {
    self: string;
    first?: string;
    next?: string;
    prev?: string;
  };
  meta?: {
    paging: {
      total: number;
      limit: number;
    };
  };
}

export interface SubscriptionsResponse {
  data: Subscription[];
  included?: any[];
  links: {
    self: string;
    first?: string;
    next?: string;
    prev?: string;
  };
  meta?: {
    paging: {
      total: number;
      limit: number;
    };
  };
}

// Processed types for easier consumption
export interface ProcessedPricePoint {
  id: string;
  customerPrice: string;
  proceeds: string;
  proceedsDate?: string;
  territory: {
    id: string;
    currency: string;
  };
  subscription: {
    id: string;
    name?: string;
    productId?: string;
  };
}

export interface PricePointsByCountry {
  [territoryId: string]: {
    territory: {
      id: string;
      currency: string;
    };
    pricePoints: ProcessedPricePoint[];
  };
}

// Query parameters for price points endpoint
export interface PricePointsQueryParams {
  'filter[territory]'?: string[];
  'fields[subscriptionPricePoints]'?: string;
  'fields[territories]'?: string;
  'fields[subscriptions]'?: string;
  include?: string;
  limit?: number;
}

// Common territory codes
export const COMMON_TERRITORIES = {
  US: 'USA',
  GB: 'GBR', 
  CA: 'CAN',
  AU: 'AUS',
  DE: 'DEU',
  FR: 'FRA',
  JP: 'JPN',
  CN: 'CHN',
  IN: 'IND',
  BR: 'BRA'
} as const;

export type TerritoryCode = keyof typeof COMMON_TERRITORIES;
