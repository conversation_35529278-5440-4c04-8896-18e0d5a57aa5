/**
 * Test script for Apple App Store Connect API authentication
 * This can be used to validate your configuration before using the UI
 */

import { createServiceFromEnv } from '../services/appStoreConnect';
import { validateAppStoreConnectConfig } from '../utils/validation';
import { parseError } from '../utils/errorHandling';

/**
 * Test authentication and basic API connectivity
 */
export async function testAuthentication(): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> {
  try {
    console.log('🔍 Testing App Store Connect API authentication...');

    // First, validate configuration
    const config = {
      keyId: process.env.APPLE_KEY_ID || '',
      issuerId: process.env.APPLE_ISSUER_ID || '',
      privateKey: process.env.APPLE_PRIVATE_KEY || ''
    };

    const validation = validateAppStoreConnectConfig(config);
    if (!validation.isValid) {
      return {
        success: false,
        message: 'Configuration validation failed',
        details: validation.errors
      };
    }

    console.log('✅ Configuration validation passed');

    // Test service creation
    const service = createServiceFromEnv();
    console.log('✅ Service created successfully');

    // Test token generation
    const token = await service.auth.getValidToken();
    console.log('✅ JWT token generated successfully');
    console.log(`Token length: ${token.length} characters`);

    // Test API connectivity by fetching subscriptions
    console.log('🔍 Testing API connectivity...');
    const result = await service.subscriptions.getAllSubscriptions();
    console.log(`✅ API connectivity test passed - found ${result.subscriptions.length} subscriptions across ${result.apps.length} apps`);

    return {
      success: true,
      message: 'Authentication and API connectivity test passed',
      details: {
        subscriptionsFound: result.subscriptions.length,
        appsFound: result.apps.length,
        tokenLength: token.length
      }
    };

  } catch (error) {
    const errorInfo = parseError(error);
    console.error('❌ Test failed:', errorInfo);

    return {
      success: false,
      message: `Test failed: ${errorInfo.title}`,
      details: {
        error: errorInfo.message,
        type: errorInfo.type,
        retryable: errorInfo.retryable,
        suggestions: errorInfo.suggestions
      }
    };
  }
}

/**
 * Test subscription price points retrieval
 */
export async function testPricePointsRetrieval(subscriptionId: string): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> {
  try {
    console.log(`🔍 Testing price points retrieval for subscription: ${subscriptionId}`);

    const service = createServiceFromEnv();
    
    // Test basic price points retrieval
    const response = await service.subscriptions.getSubscriptionPricePoints(subscriptionId, {
      limit: 10
    });

    console.log(`✅ Retrieved ${response.data.length} price points`);

    // Test processed price points
    const processedPricePoints = service.subscriptions.processePricePoints(response);
    console.log(`✅ Processed ${processedPricePoints.length} price points`);

    // Test grouping by country
    const groupedPricePoints = service.subscriptions.groupPricePointsByCountry(processedPricePoints);
    const countries = Object.keys(groupedPricePoints);
    console.log(`✅ Grouped price points by ${countries.length} countries/territories`);

    return {
      success: true,
      message: 'Price points retrieval test passed',
      details: {
        totalPricePoints: response.data.length,
        processedPricePoints: processedPricePoints.length,
        countries: countries.length,
        countriesList: countries.slice(0, 5) // Show first 5 countries
      }
    };

  } catch (error) {
    const errorInfo = parseError(error);
    console.error('❌ Price points test failed:', errorInfo);

    return {
      success: false,
      message: `Price points test failed: ${errorInfo.title}`,
      details: {
        error: errorInfo.message,
        type: errorInfo.type,
        retryable: errorInfo.retryable,
        suggestions: errorInfo.suggestions
      }
    };
  }
}

/**
 * Run all tests
 */
export async function runAllTests(subscriptionId?: string): Promise<void> {
  console.log('🚀 Starting App Store Connect API tests...\n');

  // Test 1: Authentication
  const authResult = await testAuthentication();
  console.log('\n📊 Authentication Test Result:');
  console.log(`Status: ${authResult.success ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Message: ${authResult.message}`);
  if (authResult.details) {
    console.log('Details:', authResult.details);
  }

  // Test 2: Price Points (only if auth passed and subscription ID provided)
  if (authResult.success && subscriptionId) {
    console.log('\n' + '='.repeat(50));
    const pricePointsResult = await testPricePointsRetrieval(subscriptionId);
    console.log('\n📊 Price Points Test Result:');
    console.log(`Status: ${pricePointsResult.success ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Message: ${pricePointsResult.message}`);
    if (pricePointsResult.details) {
      console.log('Details:', pricePointsResult.details);
    }
  } else if (!subscriptionId) {
    console.log('\n⚠️  Skipping price points test - no subscription ID provided');
  }

  console.log('\n🏁 Tests completed!');
}

// Export for use in browser console or Node.js
if (typeof window !== 'undefined') {
  // Browser environment
  (window as any).testAppStoreConnect = {
    testAuthentication,
    testPricePointsRetrieval,
    runAllTests
  };
}
