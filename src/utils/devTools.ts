// 1. Import the toolbar
import { initToolbar } from '@stagewise/toolbar';

// 2. Define your toolbar configuration
const stagewiseConfig = {
  plugins: [],
};

// 3. Initialize the toolbar when your app starts
// Framework-agnostic approach - call this when your app initializes
export const setupStagewise = () => {
  // Only initialize once and only in development mode
  if (import.meta.env.MODE !== 'development') {
    return;
  }
  initToolbar(stagewiseConfig);
}

// Call the setup function when appropriate for your framework
setupStagewise();