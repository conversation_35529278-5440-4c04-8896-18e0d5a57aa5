/**
 * Validation utilities for App Store Connect API integration
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Validate Apple App Store Connect API Key ID format
 */
export function validateKeyId(keyId: string): ValidationResult {
  const errors: string[] = [];

  if (!keyId) {
    errors.push('Key ID is required');
    return { isValid: false, errors };
  }

  if (keyId.length !== 10) {
    errors.push('Key ID must be exactly 10 characters long');
  }

  if (!/^[A-Z0-9]+$/.test(keyId)) {
    errors.push('Key ID must contain only uppercase letters and numbers');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate Apple App Store Connect Issuer ID format
 */
export function validateIssuerId(issuerId: string): ValidationResult {
  const errors: string[] = [];

  if (!issuerId) {
    errors.push('Issuer ID is required');
    return { isValid: false, errors };
  }

  // UUID format: 8-4-4-4-12 characters
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  
  if (!uuidRegex.test(issuerId)) {
    errors.push('Issuer ID must be a valid UUID format (e.g., 12345678-1234-1234-1234-123456789012)');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate Apple App Store Connect Private Key format
 */
export function validatePrivateKey(privateKey: string): ValidationResult {
  const errors: string[] = [];

  if (!privateKey) {
    errors.push('Private key is required');
    return { isValid: false, errors };
  }

  // Check for PEM format headers
  if (!privateKey.includes('-----BEGIN PRIVATE KEY-----')) {
    errors.push('Private key must include "-----BEGIN PRIVATE KEY-----" header');
  }

  if (!privateKey.includes('-----END PRIVATE KEY-----')) {
    errors.push('Private key must include "-----END PRIVATE KEY-----" footer');
  }

  // Check for reasonable key length (base64 encoded key should be substantial)
  const keyContent = privateKey
    .replace(/-----BEGIN PRIVATE KEY-----/g, '')
    .replace(/-----END PRIVATE KEY-----/g, '')
    .replace(/\s/g, '');

  if (keyContent.length < 100) {
    errors.push('Private key content appears to be too short');
  }

  // Basic base64 validation
  try {
    atob(keyContent);
  } catch {
    errors.push('Private key content does not appear to be valid base64');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate complete App Store Connect configuration
 */
export function validateAppStoreConnectConfig(config: {
  keyId: string;
  issuerId: string;
  privateKey: string;
}): ValidationResult {
  const allErrors: string[] = [];

  const keyIdResult = validateKeyId(config.keyId);
  const issuerIdResult = validateIssuerId(config.issuerId);
  const privateKeyResult = validatePrivateKey(config.privateKey);

  allErrors.push(...keyIdResult.errors);
  allErrors.push(...issuerIdResult.errors);
  allErrors.push(...privateKeyResult.errors);

  return {
    isValid: allErrors.length === 0,
    errors: allErrors
  };
}

/**
 * Validate subscription ID format
 */
export function validateSubscriptionId(subscriptionId: string): ValidationResult {
  const errors: string[] = [];

  if (!subscriptionId) {
    errors.push('Subscription ID is required');
    return { isValid: false, errors };
  }

  if (subscriptionId.trim() !== subscriptionId) {
    errors.push('Subscription ID should not have leading or trailing whitespace');
  }

  if (subscriptionId.length < 5) {
    errors.push('Subscription ID appears to be too short');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Sanitize and format private key for use
 */
export function sanitizePrivateKey(privateKey: string): string {
  // Remove extra whitespace and normalize line endings
  let sanitized = privateKey.replace(/\r\n/g, '\n').trim();

  // If missing headers, add them
  if (!sanitized.includes('-----BEGIN PRIVATE KEY-----')) {
    // Remove any existing headers first
    sanitized = sanitized
      .replace(/-----BEGIN [^-]+-----/g, '')
      .replace(/-----END [^-]+-----/g, '')
      .replace(/\s/g, '');

    // Add proper headers and format
    const keyBody = sanitized.match(/.{1,64}/g)?.join('\n') || sanitized;
    sanitized = `-----BEGIN PRIVATE KEY-----\n${keyBody}\n-----END PRIVATE KEY-----`;
  }

  return sanitized;
}

/**
 * Check if environment variables are properly set
 */
export function validateEnvironmentVariables(): ValidationResult {
  const errors: string[] = [];
  const requiredVars = ['APPLE_KEY_ID', 'APPLE_ISSUER_ID', 'APPLE_PRIVATE_KEY'];

  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      errors.push(`Environment variable ${varName} is not set`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
