/**
 * Request Queue Manager for controlling concurrent API requests
 * Prevents API rate limiting by managing request concurrency and delays
 */

export interface QueuedRequest<T> {
  id: string;
  execute: (signal: AbortSignal) => Promise<T>;
  resolve: (value: T) => void;
  reject: (error: any) => void;
  abortController: AbortController;
}

export class RequestQueue {
  private queue: QueuedRequest<any>[] = [];
  private running: Set<string> = new Set();
  private maxConcurrent: number;
  private delayBetweenBatches: number;
  private lastBatchTime: number = 0;

  constructor(maxConcurrent: number = 5, delayBetweenBatches: number = 1000) {
    this.maxConcurrent = maxConcurrent;
    this.delayBetweenBatches = delayBetweenBatches;
  }

  /**
   * Add a request to the queue
   */
  public async enqueue<T>(
    id: string,
    requestFn: (signal: AbortSignal) => Promise<T>
  ): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const abortController = new AbortController();
      
      const queuedRequest: QueuedRequest<T> = {
        id,
        execute: requestFn,
        resolve,
        reject,
        abortController
      };

      this.queue.push(queuedRequest);
      this.processQueue();
    });
  }

  /**
   * Cancel all requests with matching ID pattern
   */
  public cancelRequests(idPattern?: string): void {
    // Cancel running requests
    for (const runningId of this.running) {
      if (!idPattern || runningId.includes(idPattern)) {
        const request = this.queue.find(r => r.id === runningId);
        if (request) {
          request.abortController.abort();
        }
      }
    }

    // Remove queued requests
    this.queue = this.queue.filter(request => {
      const shouldCancel = !idPattern || request.id.includes(idPattern);
      if (shouldCancel) {
        request.abortController.abort();
        request.reject(new Error('Request cancelled'));
        return false;
      }
      return true;
    });
  }

  /**
   * Clear all requests
   */
  public clear(): void {
    this.cancelRequests();
    this.queue = [];
    this.running.clear();
  }

  /**
   * Process the queue with concurrency control
   */
  private async processQueue(): Promise<void> {
    if (this.running.size >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }

    // Check if we need to delay between batches
    const now = Date.now();
    if (this.running.size === 0 && this.lastBatchTime > 0) {
      const timeSinceLastBatch = now - this.lastBatchTime;
      if (timeSinceLastBatch < this.delayBetweenBatches) {
        const delay = this.delayBetweenBatches - timeSinceLastBatch;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    const request = this.queue.shift();
    if (!request) return;

    this.running.add(request.id);

    try {
      const result = await request.execute(request.abortController.signal);
      request.resolve(result);
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        request.reject(error);
      }
    } finally {
      this.running.delete(request.id);
      
      // Update last batch time when all requests in current batch are done
      if (this.running.size === 0) {
        this.lastBatchTime = Date.now();
      }
      
      // Process next request
      this.processQueue();
    }
  }

  /**
   * Get queue status
   */
  public getStatus() {
    return {
      queued: this.queue.length,
      running: this.running.size,
      maxConcurrent: this.maxConcurrent
    };
  }
}

// Global request queue instance
const MAX_CONCURRENT = parseInt(import.meta.env.VITE_MAX_CONCURRENT_PRICE_REQUESTS || '5');
export const globalRequestQueue = new RequestQueue(MAX_CONCURRENT, 1000);
