import { AppStoreConnectAPIError } from '../services/appStoreConnect';

export interface ErrorInfo {
  title: string;
  message: string;
  type: 'auth' | 'network' | 'api' | 'validation' | 'unknown';
  retryable: boolean;
  suggestions?: string[];
}

/**
 * Parse and categorize errors for user-friendly display
 */
export function parseError(error: unknown): ErrorInfo {
  // Handle App Store Connect API errors
  if (error instanceof AppStoreConnectAPIError) {
    return parseAppStoreConnectError(error);
  }

  // Handle standard JavaScript errors
  if (error instanceof Error) {
    return parseStandardError(error);
  }

  // Handle unknown errors
  return {
    title: 'Unknown Error',
    message: 'An unexpected error occurred',
    type: 'unknown',
    retryable: true,
    suggestions: ['Try refreshing the page', 'Check your internet connection']
  };
}

function parseAppStoreConnectError(error: AppStoreConnectAPIError): ErrorInfo {
  const firstError = error.errors[0];
  
  // Authentication errors
  if (error.status === 401 || firstError?.code === 'NOT_AUTHORIZED') {
    return {
      title: 'Authentication Failed',
      message: 'Unable to authenticate with Apple App Store Connect API',
      type: 'auth',
      retryable: false,
      suggestions: [
        'Check that your API credentials are correct',
        'Verify that your private key is in the correct PEM format',
        'Ensure your API key has the necessary permissions',
        'Check that your API key has not expired'
      ]
    };
  }

  // Rate limiting
  if (error.status === 429) {
    return {
      title: 'Rate Limit Exceeded',
      message: 'Too many requests to the App Store Connect API',
      type: 'api',
      retryable: true,
      suggestions: [
        'Wait a few minutes before trying again',
        'Reduce the frequency of your requests'
      ]
    };
  }

  // Forbidden access
  if (error.status === 403) {
    return {
      title: 'Access Forbidden',
      message: 'Your API key does not have permission to access this resource',
      type: 'auth',
      retryable: false,
      suggestions: [
        'Check that your API key has the correct permissions',
        'Verify that you have access to the requested subscription',
        'Contact your App Store Connect administrator'
      ]
    };
  }

  // Not found
  if (error.status === 404) {
    return {
      title: 'Resource Not Found',
      message: firstError?.detail || 'The requested resource was not found',
      type: 'api',
      retryable: false,
      suggestions: [
        'Check that the subscription ID is correct',
        'Verify that the subscription exists in your account'
      ]
    };
  }

  // Server errors
  if (error.status >= 500) {
    return {
      title: 'Server Error',
      message: 'Apple App Store Connect API is experiencing issues',
      type: 'api',
      retryable: true,
      suggestions: [
        'Try again in a few minutes',
        'Check Apple\'s system status page'
      ]
    };
  }

  // Generic API error
  return {
    title: 'API Error',
    message: firstError?.detail || error.message,
    type: 'api',
    retryable: true,
    suggestions: ['Try again', 'Check your request parameters']
  };
}

function parseStandardError(error: Error): ErrorInfo {
  const message = error.message.toLowerCase();

  // Network errors
  if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
    return {
      title: 'Network Error',
      message: 'Unable to connect to the App Store Connect API',
      type: 'network',
      retryable: true,
      suggestions: [
        'Check your internet connection',
        'Try again in a few moments',
        'Verify that you can access other websites'
      ]
    };
  }

  // Configuration errors
  if (message.includes('key') || message.includes('credential') || message.includes('config')) {
    return {
      title: 'Configuration Error',
      message: error.message,
      type: 'validation',
      retryable: false,
      suggestions: [
        'Check your environment variables',
        'Verify your API credentials are correct',
        'Ensure your private key is properly formatted'
      ]
    };
  }

  // JWT/Token errors
  if (message.includes('jwt') || message.includes('token') || message.includes('sign')) {
    return {
      title: 'Authentication Token Error',
      message: 'Failed to generate authentication token',
      type: 'auth',
      retryable: false,
      suggestions: [
        'Check that your private key is in correct PEM format',
        'Verify your Key ID and Issuer ID are correct',
        'Ensure your private key matches your Key ID'
      ]
    };
  }

  // Generic error
  return {
    title: 'Error',
    message: error.message,
    type: 'unknown',
    retryable: true,
    suggestions: ['Try again', 'Check the console for more details']
  };
}

/**
 * Get user-friendly error message for display
 */
export function getErrorDisplayMessage(error: unknown): string {
  const errorInfo = parseError(error);
  return `${errorInfo.title}: ${errorInfo.message}`;
}

/**
 * Check if an error is retryable
 */
export function isRetryableError(error: unknown): boolean {
  const errorInfo = parseError(error);
  return errorInfo.retryable;
}

/**
 * Get suggestions for resolving an error
 */
export function getErrorSuggestions(error: unknown): string[] {
  const errorInfo = parseError(error);
  return errorInfo.suggestions || [];
}
