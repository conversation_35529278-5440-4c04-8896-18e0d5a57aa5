import React from 'react';
import type { ProcessedPricePoint, PricePointsByCountry } from '../services/appStoreConnect/types';

interface PricePointsDisplayProps {
  pricePointsByCountry: PricePointsByCountry;
  loading?: boolean;
  error?: string;
  retryable?: boolean;
  onRetry?: () => void;
}

export const PricePointsDisplay: React.FC<PricePointsDisplayProps> = ({
  pricePointsByCountry,
  loading = false,
  error,
  retryable = false,
  onRetry
}) => {
  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading subscription price points...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <h3>Error Loading Price Points</h3>
        <p>{error}</p>
        {retryable && onRetry && (
          <button onClick={onRetry} className="retry-btn">
            Try Again
          </button>
        )}
      </div>
    );
  }

  const territories = Object.keys(pricePointsByCountry);

  if (territories.length === 0) {
    return (
      <div className="no-data-container">
        <p>No price points found for this subscription.</p>
      </div>
    );
  }

  return (
    <div className="price-points-container">
      <h2>Subscription Price Points by Country</h2>
      <div className="territories-grid">
        {territories.map(territoryId => {
          const territoryData = pricePointsByCountry[territoryId];
          return (
            <TerritoryCard
              key={territoryId}
              territoryId={territoryId}
              territory={territoryData.territory}
              pricePoints={territoryData.pricePoints}
            />
          );
        })}
      </div>
    </div>
  );
};

interface TerritoryCardProps {
  territoryId: string;
  territory: {
    id: string;
    currency: string;
  };
  pricePoints: ProcessedPricePoint[];
}

const TerritoryCard: React.FC<TerritoryCardProps> = ({
  territoryId,
  territory,
  pricePoints
}) => {
  return (
    <div className="territory-card">
      <div className="territory-header">
        <h3>{territoryId}</h3>
        <span className="currency-badge">{territory.currency}</span>
      </div>
      <div className="price-points-list">
        {pricePoints.map(pricePoint => (
          <PricePointItem key={pricePoint.id} pricePoint={pricePoint} />
        ))}
      </div>
    </div>
  );
};

interface PricePointItemProps {
  pricePoint: ProcessedPricePoint;
}

const PricePointItem: React.FC<PricePointItemProps> = ({ pricePoint }) => {
  return (
    <div className="price-point-item">
      <div className="price-info">
        <div className="customer-price">
          <label>Customer Price:</label>
          <span>{pricePoint.customerPrice}</span>
        </div>
        <div className="proceeds">
          <label>Proceeds:</label>
          <span>{pricePoint.proceeds}</span>
        </div>
        {pricePoint.proceedsDate && (
          <div className="proceeds-date">
            <label>Proceeds Date:</label>
            <span>{new Date(pricePoint.proceedsDate).toLocaleDateString()}</span>
          </div>
        )}
      </div>
      {pricePoint.subscription.name && (
        <div className="subscription-info">
          <small>
            {pricePoint.subscription.name}
            {pricePoint.subscription.productId && (
              <span> ({pricePoint.subscription.productId})</span>
            )}
          </small>
        </div>
      )}
    </div>
  );
};
