import React, { useState } from 'react';
import { TERRITORIES } from '../services/appStoreConnect/types';
import type { TerritoryCode } from '../services/appStoreConnect/types';

interface TerritorySelectorProps {
  selectedTerritories: TerritoryCode[];
  onSelectionChange: (territories: TerritoryCode[]) => void;
  disabled?: boolean;
}

export const TerritorySelector: React.FC<TerritorySelectorProps> = ({
  selectedTerritories,
  onSelectionChange,
  disabled = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const handleTerritoryToggle = (territory: TerritoryCode) => {
    if (selectedTerritories.includes(territory)) {
      onSelectionChange(selectedTerritories.filter(t => t !== territory));
    } else {
      onSelectionChange([...selectedTerritories, territory]);
    }
  };

  const handleSelectAll = () => {
    if (selectedTerritories.length === Object.keys(TERRITORIES).length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(Object.keys(TERRITORIES) as TerritoryCode[]);
    }
  };

  const territoryEntries = Object.entries(TERRITORIES) as [TerritoryCode, typeof TERRITORIES[TerritoryCode]][];
  
  // Filter territories based on search term
  const filteredTerritories = territoryEntries.filter(([code, info]) => 
    code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    info.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    info.currency.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Group territories by region for better organization
  const territoryGroups = {
    'North America': ['US', 'CA', 'MX'],
    'Europe': ['GB', 'DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'NO', 'DK', 'CH'],
    'Asia Pacific': ['JP', 'CN', 'KR', 'IN', 'AU', 'NZ', 'SG', 'HK', 'TW'],
    'Other Markets': ['BR', 'RU', 'TR', 'ZA', 'IL', 'AE', 'SA']
  };

  return (
    <div className="territory-selector">
      <button
        type="button"
        onClick={() => setIsExpanded(!isExpanded)}
        className="territory-toggle"
        disabled={disabled}
      >
        <span>Filter by Countries/Territories</span>
        <div className="toggle-info">
          <span className="selection-count">
            {selectedTerritories.length > 0 
              ? `${selectedTerritories.length} selected`
              : 'All territories'
            }
          </span>
          <span className={`arrow ${isExpanded ? 'expanded' : ''}`}>▼</span>
        </div>
      </button>

      {isExpanded && (
        <div className="territory-dropdown">
          <div className="territory-controls">
            <input
              type="text"
              placeholder="Search territories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            <button
              type="button"
              onClick={handleSelectAll}
              className="select-all-btn"
              disabled={disabled}
            >
              {selectedTerritories.length === territoryEntries.length ? 'Deselect All' : 'Select All'}
            </button>
          </div>

          <div className="territory-list">
            {searchTerm ? (
              // Show filtered results when searching
              <div className="territory-group">
                <div className="group-header">Search Results</div>
                {filteredTerritories.length > 0 ? (
                  filteredTerritories.map(([code, info]) => (
                    <label key={code} className="territory-item">
                      <input
                        type="checkbox"
                        checked={selectedTerritories.includes(code)}
                        onChange={() => handleTerritoryToggle(code)}
                        disabled={disabled}
                      />
                      <div className="territory-info">
                        <span className="territory-name">{info.name}</span>
                        <span className="territory-details">({code}) - {info.currency}</span>
                      </div>
                    </label>
                  ))
                ) : (
                  <div className="no-results">No territories found</div>
                )}
              </div>
            ) : (
              // Show grouped results when not searching
              Object.entries(territoryGroups).map(([groupName, codes]) => (
                <div key={groupName} className="territory-group">
                  <div className="group-header">{groupName}</div>
                  {codes.map(code => {
                    const territoryCode = code as TerritoryCode;
                    const info = TERRITORIES[territoryCode];
                    return (
                      <label key={territoryCode} className="territory-item">
                        <input
                          type="checkbox"
                          checked={selectedTerritories.includes(territoryCode)}
                          onChange={() => handleTerritoryToggle(territoryCode)}
                          disabled={disabled}
                        />
                        <div className="territory-info">
                          <span className="territory-name">{info.name}</span>
                          <span className="territory-details">({territoryCode}) - {info.currency}</span>
                        </div>
                      </label>
                    );
                  })}
                </div>
              ))
            )}
          </div>
        </div>
      )}


    </div>
  );
};
