import React from 'react';
import type { App } from '../services/appStoreConnect/types';

interface AppSelectorProps {
  apps: App[];
  selectedAppIds: string[];
  onSelectionChange: (selectedAppIds: string[]) => void;
  loading?: boolean;
  disabled?: boolean;
}

export const AppSelector: React.FC<AppSelectorProps> = ({
  apps,
  selectedAppIds,
  onSelectionChange,
  loading = false,
  disabled = false
}) => {
  const handleAppToggle = (appId: string) => {
    if (selectedAppIds.includes(appId)) {
      onSelectionChange(selectedAppIds.filter(id => id !== appId));
    } else {
      onSelectionChange([...selectedAppIds, appId]);
    }
  };

  const handleSelectAll = () => {
    if (selectedAppIds.length === apps.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(apps.map(app => app.id));
    }
  };

  const isAllSelected = selectedAppIds.length === apps.length && apps.length > 0;
  const isPartiallySelected = selectedAppIds.length > 0 && selectedAppIds.length < apps.length;

  return (
    <div className="app-selector">
      <div className="filter-header">
        <h3>Filter by Apps</h3>
        <div className="selection-summary">
          {selectedAppIds.length > 0 ? (
            <span className="selected-count">
              {selectedAppIds.length} of {apps.length} selected
            </span>
          ) : (
            <span className="no-selection">All apps</span>
          )}
        </div>
      </div>

      <div className="filter-controls">
        <button
          type="button"
          onClick={handleSelectAll}
          disabled={disabled || loading || apps.length === 0}
          className={`select-all-btn ${isAllSelected ? 'selected' : ''} ${isPartiallySelected ? 'partial' : ''}`}
        >
          {isAllSelected ? 'Deselect All' : 'Select All'}
        </button>
      </div>

      <div className="app-list">
        {loading ? (
          <div className="loading-state">Loading apps...</div>
        ) : apps.length === 0 ? (
          <div className="empty-state">No apps found</div>
        ) : (
          apps.map(app => (
            <label key={app.id} className="app-item">
              <input
                type="checkbox"
                checked={selectedAppIds.includes(app.id)}
                onChange={() => handleAppToggle(app.id)}
                disabled={disabled}
              />
              <div className="app-info">
                <div className="app-name">{app.attributes.name}</div>
                <div className="app-bundle-id">{app.attributes.bundleId}</div>
                {app.attributes.sku && (
                  <div className="app-sku">SKU: {app.attributes.sku}</div>
                )}
              </div>
            </label>
          ))
        )}
      </div>


    </div>
  );
};
