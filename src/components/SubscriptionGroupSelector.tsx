import React from 'react';
import type { SubscriptionGroup } from '../services/appStoreConnect/types';

interface SubscriptionGroupWithApp extends SubscriptionGroup {
  appName: string;
  appId: string;
}

interface SubscriptionGroupSelectorProps {
  subscriptionGroups: SubscriptionGroupWithApp[];
  selectedGroupIds: string[];
  onSelectionChange: (selectedGroupIds: string[]) => void;
  loading?: boolean;
  disabled?: boolean;
  selectedAppIds?: string[];
}

export const SubscriptionGroupSelector: React.FC<SubscriptionGroupSelectorProps> = ({
  subscriptionGroups,
  selectedGroupIds,
  onSelectionChange,
  loading = false,
  disabled = false,
  selectedAppIds = []
}) => {
  // Filter groups based on selected apps
  const filteredGroups = selectedAppIds.length > 0 
    ? subscriptionGroups.filter(group => selectedAppIds.includes(group.appId))
    : subscriptionGroups;

  const handleGroupToggle = (groupId: string) => {
    if (selectedGroupIds.includes(groupId)) {
      onSelectionChange(selectedGroupIds.filter(id => id !== groupId));
    } else {
      onSelectionChange([...selectedGroupIds, groupId]);
    }
  };

  const handleSelectAll = () => {
    if (selectedGroupIds.length === filteredGroups.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(filteredGroups.map(group => group.id));
    }
  };

  const isAllSelected = selectedGroupIds.length === filteredGroups.length && filteredGroups.length > 0;
  const isPartiallySelected = selectedGroupIds.length > 0 && selectedGroupIds.length < filteredGroups.length;

  // Group by app for better organization
  const groupsByApp = filteredGroups.reduce((acc, group) => {
    if (!acc[group.appId]) {
      acc[group.appId] = {
        appName: group.appName,
        groups: []
      };
    }
    acc[group.appId].groups.push(group);
    return acc;
  }, {} as Record<string, { appName: string; groups: SubscriptionGroupWithApp[] }>);

  return (
    <div className="subscription-group-selector">
      <div className="filter-header">
        <h3>Filter by Subscription Groups</h3>
        <div className="selection-summary">
          {selectedGroupIds.length > 0 ? (
            <span className="selected-count">
              {selectedGroupIds.length} of {filteredGroups.length} selected
            </span>
          ) : (
            <span className="no-selection">All groups</span>
          )}
        </div>
      </div>

      {selectedAppIds.length > 0 && (
        <div className="filter-context">
          <small>Showing groups from {selectedAppIds.length} selected app(s)</small>
        </div>
      )}

      <div className="filter-controls">
        <button
          type="button"
          onClick={handleSelectAll}
          disabled={disabled || loading || filteredGroups.length === 0}
          className={`select-all-btn ${isAllSelected ? 'selected' : ''} ${isPartiallySelected ? 'partial' : ''}`}
        >
          {isAllSelected ? 'Deselect All' : 'Select All'}
        </button>
      </div>

      <div className="group-list">
        {loading ? (
          <div className="loading-state">Loading subscription groups...</div>
        ) : filteredGroups.length === 0 ? (
          <div className="empty-state">
            {selectedAppIds.length > 0 
              ? 'No subscription groups found for selected apps'
              : 'No subscription groups found'
            }
          </div>
        ) : (
          Object.entries(groupsByApp).map(([appId, { appName, groups }]) => (
            <div key={appId} className="app-section">
              <div className="app-header">{appName}</div>
              {groups.map(group => (
                <label key={group.id} className="group-item">
                  <input
                    type="checkbox"
                    checked={selectedGroupIds.includes(group.id)}
                    onChange={() => handleGroupToggle(group.id)}
                    disabled={disabled}
                  />
                  <div className="group-info">
                    <div className="group-name">{group.attributes.referenceName}</div>
                    <div className="group-id">ID: {group.id}</div>
                  </div>
                </label>
              ))}
            </div>
          ))
        )}
      </div>


    </div>
  );
};
