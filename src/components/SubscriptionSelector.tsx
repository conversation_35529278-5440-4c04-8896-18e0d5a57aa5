import React from 'react';
import type { Subscription } from '../services/appStoreConnect/types';

interface SubscriptionSelectorProps {
  subscriptions: Array<Subscription & { appName: string, groupName: string }>;
  selectedSubscriptionId: string;
  onSubscriptionChange: (subscriptionId: string) => void;
  loading?: boolean;
  onFetchPricePoints: () => void;
}

export const SubscriptionSelector: React.FC<SubscriptionSelectorProps> = ({
  subscriptions,
  selectedSubscriptionId,
  onSubscriptionChange,
  loading = false,
  onFetchPricePoints
}) => {

  return (
    <div className="subscription-selector">
      <div className="selector-section">
        <h3>Select Subscription</h3>
        <select
          value={selectedSubscriptionId}
          onChange={(e) => onSubscriptionChange(e.target.value)}
          className="subscription-dropdown"
          disabled={loading}
        >
          <option value="">Select a subscription...</option>
          {subscriptions.map(subscription => (
            <option key={subscription.id} value={subscription.id}>
              {subscription.attributes.name} - {subscription.appName} ({subscription.groupName})
            </option>
          ))}
        </select>
      </div>



      <div className="selector-section">
        <button
          onClick={onFetchPricePoints}
          disabled={!selectedSubscriptionId || loading}
          className="fetch-btn"
        >
          {loading ? 'Loading...' : 'Fetch Price Points'}
        </button>
      </div>

      {selectedSubscriptionId && (
        <div className="selected-subscription-info">
          <h4>Selected Subscription Details</h4>
          {subscriptions
            .filter(sub => sub.id === selectedSubscriptionId)
            .map(subscription => (
              <div key={subscription.id} className="subscription-details">
                <p><strong>Name:</strong> {subscription.attributes.name}</p>
                <p><strong>App:</strong> {subscription.appName}</p>
                <p><strong>Group:</strong> {subscription.groupName}</p>
                <p><strong>Product ID:</strong> {subscription.attributes.productId}</p>
                <p><strong>State:</strong> {subscription.attributes.state}</p>
                <p><strong>Period:</strong> {subscription.attributes.subscriptionPeriod}</p>
                <p><strong>Family Sharable:</strong> {subscription.attributes.familySharable ? 'Yes' : 'No'}</p>
              </div>
            ))}
        </div>
      )}
    </div>
  );
};
