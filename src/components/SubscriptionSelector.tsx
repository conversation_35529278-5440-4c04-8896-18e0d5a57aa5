import React, { useState } from 'react';
import { COMMON_TERRITORIES } from '../services/appStoreConnect/types';
import type { Subscription, TerritoryCode } from '../services/appStoreConnect/types';

interface SubscriptionSelectorProps {
  subscriptions: Array<Subscription & { appName: string, groupName: string }>;
  selectedSubscriptionId: string;
  onSubscriptionChange: (subscriptionId: string) => void;
  selectedTerritories: TerritoryCode[];
  onTerritoriesChange: (territories: TerritoryCode[]) => void;
  loading?: boolean;
  onFetchPricePoints: () => void;
}

export const SubscriptionSelector: React.FC<SubscriptionSelectorProps> = ({
  subscriptions,
  selectedSubscriptionId,
  onSubscriptionChange,
  selectedTerritories,
  onTerritoriesChange,
  loading = false,
  onFetchPricePoints
}) => {
  const [showTerritorySelector, setShowTerritorySelector] = useState(false);

  const handleTerritoryToggle = (territoryCode: TerritoryCode) => {
    const isSelected = selectedTerritories.includes(territoryCode);
    if (isSelected) {
      onTerritoriesChange(selectedTerritories.filter(t => t !== territoryCode));
    } else {
      onTerritoriesChange([...selectedTerritories, territoryCode]);
    }
  };

  const handleSelectAllTerritories = () => {
    const allTerritories = Object.keys(COMMON_TERRITORIES) as TerritoryCode[];
    onTerritoriesChange(allTerritories);
  };

  const handleClearTerritories = () => {
    onTerritoriesChange([]);
  };

  return (
    <div className="subscription-selector">
      <div className="selector-section">
        <h3>Select Subscription</h3>
        <select
          value={selectedSubscriptionId}
          onChange={(e) => onSubscriptionChange(e.target.value)}
          className="subscription-dropdown"
          disabled={loading}
        >
          <option value="">Select a subscription...</option>
          {subscriptions.map(subscription => (
            <option key={subscription.id} value={subscription.id}>
              {subscription.attributes.name} - {subscription.appName} ({subscription.groupName})
            </option>
          ))}
        </select>
      </div>

      <div className="selector-section">
        <h3>Filter by Countries/Territories</h3>
        <div className="territory-controls">
          <button
            type="button"
            onClick={() => setShowTerritorySelector(!showTerritorySelector)}
            className="territory-toggle-btn"
          >
            {showTerritorySelector ? 'Hide' : 'Show'} Territory Filter
            ({selectedTerritories.length} selected)
          </button>
          
          {showTerritorySelector && (
            <div className="territory-selector">
              <div className="territory-actions">
                <button
                  type="button"
                  onClick={handleSelectAllTerritories}
                  className="territory-action-btn"
                >
                  Select All
                </button>
                <button
                  type="button"
                  onClick={handleClearTerritories}
                  className="territory-action-btn"
                >
                  Clear All
                </button>
              </div>
              
              <div className="territory-checkboxes">
                {(Object.keys(COMMON_TERRITORIES) as TerritoryCode[]).map(territoryCode => (
                  <label key={territoryCode} className="territory-checkbox">
                    <input
                      type="checkbox"
                      checked={selectedTerritories.includes(territoryCode)}
                      onChange={() => handleTerritoryToggle(territoryCode)}
                    />
                    <span className="territory-label">
                      {territoryCode} ({COMMON_TERRITORIES[territoryCode]})
                    </span>
                  </label>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="selector-section">
        <button
          onClick={onFetchPricePoints}
          disabled={!selectedSubscriptionId || loading}
          className="fetch-btn"
        >
          {loading ? 'Loading...' : 'Fetch Price Points'}
        </button>
      </div>

      {selectedSubscriptionId && (
        <div className="selected-subscription-info">
          <h4>Selected Subscription Details</h4>
          {subscriptions
            .filter(sub => sub.id === selectedSubscriptionId)
            .map(subscription => (
              <div key={subscription.id} className="subscription-details">
                <p><strong>Name:</strong> {subscription.attributes.name}</p>
                <p><strong>App:</strong> {subscription.appName}</p>
                <p><strong>Group:</strong> {subscription.groupName}</p>
                <p><strong>Product ID:</strong> {subscription.attributes.productId}</p>
                <p><strong>State:</strong> {subscription.attributes.state}</p>
                <p><strong>Period:</strong> {subscription.attributes.subscriptionPeriod}</p>
                <p><strong>Family Sharable:</strong> {subscription.attributes.familySharable ? 'Yes' : 'No'}</p>
              </div>
            ))}
        </div>
      )}
    </div>
  );
};
