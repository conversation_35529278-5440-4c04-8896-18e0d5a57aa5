import React, { useState, useEffect } from 'react';
import type { Subscription, TerritoryCode, ProcessedPricePoint } from '../services/appStoreConnect/types';
import { createServiceFromEnv } from '../services/appStoreConnect';

interface SubscriptionsTableProps {
  subscriptions: Array<Subscription & { appName: string, groupName: string, appId: string, groupId: string }>;
  selectedTerritories: TerritoryCode[];
  loading?: boolean;
}

interface SubscriptionWithPrices extends Subscription {
  appName: string;
  groupName: string;
  appId: string;
  groupId: string;
  pricePoints?: ProcessedPricePoint[];
  pricesLoading?: boolean;
  pricesError?: string;
  eurPrice?: string;
  usdPrice?: string;
  priceLoading?: boolean;
}

type Currency = 'EUR' | 'USD';

export const SubscriptionsTable: React.FC<SubscriptionsTableProps> = ({
  subscriptions,
  selectedTerritories,
  loading = false
}) => {
  const [subscriptionsWithPrices, setSubscriptionsWithPrices] = useState<SubscriptionWithPrices[]>([]);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>('EUR');

  // Initialize subscriptions data and load EUR prices by default
  useEffect(() => {
    const initialSubscriptions = subscriptions.map(sub => ({
      ...sub,
      pricePoints: undefined,
      pricesLoading: false,
      pricesError: undefined,
      priceLoading: true
    }));
    setSubscriptionsWithPrices(initialSubscriptions);

    // Load EUR prices for all subscriptions
    initialSubscriptions.forEach(sub => {
      fetchCurrencyPrice(sub.id, 'EUR');
    });
  }, [subscriptions]);

  // Fetch price for a specific currency (EUR or USD)
  const fetchCurrencyPrice = async (subscriptionId: string, currency: Currency) => {
    console.log(`Fetching ${currency} price for subscription ${subscriptionId}`);
    try {
      const service = createServiceFromEnv();
      const territoryCode = currency === 'EUR' ? 'DE' : 'US';

      const pricePointsResponse = await service.subscriptions.getSubscriptionPricePointsByCountry(
        subscriptionId,
        [territoryCode]
      );

      console.log(`Price response for ${currency}:`, pricePointsResponse);

      // Extract the price for the specific territory
      const territoryData = Object.values(pricePointsResponse)[0];
      console.log(`Territory data for ${currency}:`, territoryData);
      const price = territoryData?.pricePoints[0]?.customerPrice || 'N/A';
      console.log(`Extracted price for ${currency}:`, price);

      setSubscriptionsWithPrices(prev => prev.map(sub =>
        sub.id === subscriptionId
          ? {
              ...sub,
              [currency === 'EUR' ? 'eurPrice' : 'usdPrice']: price,
              priceLoading: false
            }
          : sub
      ));
    } catch (error) {
      console.error(`Error fetching ${currency} price:`, error);
      setSubscriptionsWithPrices(prev => prev.map(sub =>
        sub.id === subscriptionId
          ? {
              ...sub,
              [currency === 'EUR' ? 'eurPrice' : 'usdPrice']: 'Error',
              priceLoading: false
            }
          : sub
      ));
    }
  };

  // Handle currency toggle
  const handleCurrencyToggle = (currency: Currency) => {
    setSelectedCurrency(currency);

    // Load prices for the new currency if not already loaded
    subscriptionsWithPrices.forEach(sub => {
      const priceField = currency === 'EUR' ? 'eurPrice' : 'usdPrice';
      if (!sub[priceField] && !sub.priceLoading) {
        setSubscriptionsWithPrices(prev => prev.map(s =>
          s.id === sub.id ? { ...s, priceLoading: true } : s
        ));
        fetchCurrencyPrice(sub.id, currency);
      }
    });
  };

  const fetchPricePoints = async (subscriptionId: string) => {
    setSubscriptionsWithPrices(prev => prev.map(sub => 
      sub.id === subscriptionId 
        ? { ...sub, pricesLoading: true, pricesError: undefined }
        : sub
    ));

    try {
      const service = createServiceFromEnv();
      const territories = selectedTerritories.length > 0 ? selectedTerritories : undefined;
      const pricePointsResponse = await service.subscriptions.getSubscriptionPricePointsByCountry(
        subscriptionId,
        territories
      );
      
      // Convert grouped price points to flat array
      const allPricePoints: ProcessedPricePoint[] = [];
      Object.values(pricePointsResponse).forEach(territoryData => {
        allPricePoints.push(...territoryData.pricePoints);
      });

      setSubscriptionsWithPrices(prev => prev.map(sub => 
        sub.id === subscriptionId 
          ? { ...sub, pricePoints: allPricePoints, pricesLoading: false }
          : sub
      ));
    } catch (error) {
      console.error('Error fetching price points:', error);
      setSubscriptionsWithPrices(prev => prev.map(sub => 
        sub.id === subscriptionId 
          ? { ...sub, pricesLoading: false, pricesError: 'Failed to load prices' }
          : sub
      ));
    }
  };

  const toggleRowExpansion = (subscriptionId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(subscriptionId)) {
      newExpanded.delete(subscriptionId);
    } else {
      newExpanded.add(subscriptionId);
      // Fetch price points if not already loaded
      const subscription = subscriptionsWithPrices.find(sub => sub.id === subscriptionId);
      if (subscription && !subscription.pricePoints && !subscription.pricesLoading) {
        fetchPricePoints(subscriptionId);
      }
    }
    setExpandedRows(newExpanded);
  };

  const formatPrice = (price: string, currency: string) => {
    const numPrice = parseFloat(price);
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(numPrice);
  };

  const getUniquePrice = (pricePoints: ProcessedPricePoint[]) => {
    if (!pricePoints || pricePoints.length === 0) return null;
    
    // Group by territory and get the first price point for each
    const territoryPrices = new Map();
    pricePoints.forEach(pp => {
      if (!territoryPrices.has(pp.territory.id)) {
        territoryPrices.set(pp.territory.id, pp);
      }
    });
    
    return Array.from(territoryPrices.values());
  };

  if (loading) {
    return (
      <div className="subscriptions-table-container">
        <h3>Subscriptions</h3>
        <div className="loading-state">Loading subscriptions...</div>
      </div>
    );
  }

  if (subscriptionsWithPrices.length === 0) {
    return (
      <div className="subscriptions-table-container">
        <h3>Subscriptions</h3>
        <div className="empty-state">No subscriptions found</div>
      </div>
    );
  }

  return (
    <div className="subscriptions-table-container">
      <h3>Subscriptions ({subscriptionsWithPrices.length})</h3>
      
      <div className="table-wrapper">
        <table className="subscriptions-table">
          <thead>
            <tr>
              <th>Name</th>
              <th>App</th>
              <th>Group</th>
              <th>Product ID</th>
              <th>Period</th>
              <th>State</th>
              <th>Family Sharing</th>
              <th>
                <div className="prices-header">
                  <span>Prices</span>
                  <div className="currency-toggle">
                    <button
                      className={`currency-btn ${selectedCurrency === 'EUR' ? 'active' : ''}`}
                      onClick={() => handleCurrencyToggle('EUR')}
                    >
                      EUR
                    </button>
                    <button
                      className={`currency-btn ${selectedCurrency === 'USD' ? 'active' : ''}`}
                      onClick={() => handleCurrencyToggle('USD')}
                    >
                      USD
                    </button>
                  </div>
                </div>
              </th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {subscriptionsWithPrices.map((subscription) => {
              const isExpanded = expandedRows.has(subscription.id);
              const uniquePrices = getUniquePrice(subscription.pricePoints || []);
              
              return (
                <React.Fragment key={subscription.id}>
                  <tr className="subscription-row">
                    <td className="subscription-name">
                      {subscription.attributes.name}
                    </td>
                    <td className="app-name">
                      {subscription.appName}
                    </td>
                    <td className="group-name">
                      {subscription.groupName}
                    </td>
                    <td className="product-id">
                      <code>{subscription.attributes.productId}</code>
                    </td>
                    <td className="period">
                      {subscription.attributes.subscriptionPeriod?.replace('_', ' ')}
                    </td>
                    <td className="state">
                      <span className={`state-badge ${subscription.attributes.state?.toLowerCase()}`}>
                        {subscription.attributes.state?.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="family-sharing">
                      {subscription.attributes.familySharable ? 'Yes' : 'No'}
                    </td>
                    <td className="prices">
                      {subscription.priceLoading ? (
                        <span className="loading-text">Loading...</span>
                      ) : (() => {
                        const price = selectedCurrency === 'EUR' ? subscription.eurPrice : subscription.usdPrice;
                        const symbol = selectedCurrency === 'EUR' ? '€' : '$';

                        if (price && price !== 'N/A' && price !== 'Error') {
                          return <span className="price-value">{symbol}{price}</span>;
                        } else if (price === 'Error') {
                          return <span className="error-text">Error</span>;
                        } else {
                          return <span className="no-prices">-</span>;
                        }
                      })()}
                    </td>
                    <td className="actions">
                      <button
                        onClick={() => toggleRowExpansion(subscription.id)}
                        className="expand-btn"
                        disabled={subscription.pricesLoading}
                      >
                        {isExpanded ? '▼' : '▶'} {isExpanded ? 'Hide' : 'Show'} Details
                      </button>
                    </td>
                  </tr>
                  
                  {isExpanded && (
                    <tr className="expanded-row">
                      <td colSpan={9}>
                        <div className="subscription-details">
                          <div className="details-section">
                            <h4>Subscription Details</h4>
                            <div className="details-grid">
                              <div className="detail-item">
                                <strong>Review Note:</strong>
                                <span>{subscription.attributes.reviewNote || 'None'}</span>
                              </div>
                              <div className="detail-item">
                                <strong>Group Level:</strong>
                                <span>{subscription.attributes.groupLevel || 'N/A'}</span>
                              </div>
                            </div>
                          </div>
                          
                          {subscription.pricesLoading ? (
                            <div className="loading-section">
                              <div className="loading-spinner"></div>
                              <span>Loading price points...</span>
                            </div>
                          ) : subscription.pricesError ? (
                            <div className="error-section">
                              <span className="error-message">{subscription.pricesError}</span>
                              <button 
                                onClick={() => fetchPricePoints(subscription.id)}
                                className="retry-btn"
                              >
                                Retry
                              </button>
                            </div>
                          ) : uniquePrices && uniquePrices.length > 0 ? (
                            <div className="prices-section">
                              <h4>Price Points by Territory</h4>
                              <div className="prices-grid">
                                {uniquePrices.map((pricePoint) => (
                                  <div key={pricePoint.territory.id} className="price-card">
                                    <div className="territory-info">
                                      <strong>{pricePoint.territory.currency}</strong>
                                    </div>
                                    <div className="price-info">
                                      <div className="customer-price">
                                        <label>Customer Price:</label>
                                        <span>{formatPrice(pricePoint.customerPrice, pricePoint.territory.currency)}</span>
                                      </div>
                                      <div className="proceeds">
                                        <label>Proceeds:</label>
                                        <span>{formatPrice(pricePoint.proceeds, pricePoint.territory.currency)}</span>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ) : (
                            <div className="no-prices-section">
                              <p>No price points available for this subscription.</p>
                              <button 
                                onClick={() => fetchPricePoints(subscription.id)}
                                className="load-prices-btn"
                              >
                                Load Price Points
                              </button>
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};
