import { useState, useEffect } from 'react';
import { PricePointsDisplay } from './components/PricePointsDisplay';
import { SubscriptionSelector } from './components/SubscriptionSelector';
import { AppSelector } from './components/AppSelector';
import { SubscriptionGroupSelector } from './components/SubscriptionGroupSelector';
import { TerritorySelector } from './components/TerritorySelector';
import {
  createServiceFromEnv,
  isAppStoreConnectConfigured
} from './services/appStoreConnect';
import type {
  Subscription,
  PricePointsByCountry,
  TerritoryCode,
  App,
  SubscriptionGroup
} from './services/appStoreConnect';
import { parseError } from './utils/errorHandling';
import './App.css';

interface SubscriptionGroupWithApp extends SubscriptionGroup {
  appName: string;
  appId: string;
}

function App() {
  // Core data state
  const [apps, setApps] = useState<App[]>([]);
  const [subscriptionGroups, setSubscriptionGroups] = useState<SubscriptionGroupWithApp[]>([]);
  const [subscriptions, setSubscriptions] = useState<Array<Subscription & { appName: string, groupName: string, appId: string, groupId: string }>>([]);

  // Filter state
  const [selectedAppIds, setSelectedAppIds] = useState<string[]>([]);
  const [selectedGroupIds, setSelectedGroupIds] = useState<string[]>([]);
  const [selectedSubscriptionId, setSelectedSubscriptionId] = useState<string>('');
  const [selectedTerritories, setSelectedTerritories] = useState<TerritoryCode[]>([]);

  // UI state
  const [pricePointsByCountry, setPricePointsByCountry] = useState<PricePointsByCountry>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [configError, setConfigError] = useState<string>('');
  const [retryable, setRetryable] = useState(false);

  // Check configuration on mount
  useEffect(() => {
    if (!isAppStoreConnectConfigured()) {
      setConfigError(
        'App Store Connect API is not configured. Please set the required environment variables: ' +
        'APPLE_KEY_ID, APPLE_ISSUER_ID, and APPLE_PRIVATE_KEY'
      );
      return;
    }

    // Load subscriptions on mount
    loadSubscriptions();
  }, []);

  const loadSubscriptions = async () => {
    setLoading(true);
    setError('');

    try {
      const service = createServiceFromEnv();
      const result = await service.subscriptions.getAllSubscriptions();

      // Set apps data
      setApps(result.apps);

      // Build subscription groups with app context
      const groupsWithApp: SubscriptionGroupWithApp[] = [];
      for (const app of result.apps) {
        try {
          const groups = await service.subscriptions.getSubscriptionGroups(app.id);
          groups.forEach(group => {
            groupsWithApp.push({
              ...group,
              appName: app.attributes?.name || app.id,
              appId: app.id
            });
          });
        } catch (error) {
          console.warn(`Failed to load groups for app ${app.id}:`, error);
        }
      }
      setSubscriptionGroups(groupsWithApp);

      // Set subscriptions with enhanced context
      const enhancedSubscriptions = result.subscriptions.map(sub => ({
        ...sub,
        appId: groupsWithApp.find(g => g.attributes.referenceName === sub.groupName)?.appId || '',
        groupId: groupsWithApp.find(g => g.attributes.referenceName === sub.groupName)?.id || ''
      }));
      setSubscriptions(enhancedSubscriptions);
    } catch (err) {
      const errorInfo = parseError(err);
      setError(`${errorInfo.title}: ${errorInfo.message}`);
      setRetryable(errorInfo.retryable);
    } finally {
      setLoading(false);
    }
  };

  // Filter subscriptions based on selected apps and groups
  const filteredSubscriptions = subscriptions.filter(subscription => {
    const appMatch = selectedAppIds.length === 0 || selectedAppIds.includes(subscription.appId);
    const groupMatch = selectedGroupIds.length === 0 || selectedGroupIds.includes(subscription.groupId);
    return appMatch && groupMatch;
  });

  // Handle filter changes with cascading effects
  const handleAppSelectionChange = (newSelectedAppIds: string[]) => {
    setSelectedAppIds(newSelectedAppIds);

    // Clear group selection if selected apps change
    if (selectedGroupIds.length > 0) {
      const validGroupIds = subscriptionGroups
        .filter(group => newSelectedAppIds.length === 0 || newSelectedAppIds.includes(group.appId))
        .map(group => group.id);
      const filteredGroupIds = selectedGroupIds.filter(id => validGroupIds.includes(id));
      setSelectedGroupIds(filteredGroupIds);
    }

    // Clear subscription selection if it's no longer valid
    if (selectedSubscriptionId) {
      const validSubscription = subscriptions.find(sub =>
        sub.id === selectedSubscriptionId &&
        (newSelectedAppIds.length === 0 || newSelectedAppIds.includes(sub.appId))
      );
      if (!validSubscription) {
        setSelectedSubscriptionId('');
        setPricePointsByCountry({});
      }
    }
  };

  const handleGroupSelectionChange = (newSelectedGroupIds: string[]) => {
    setSelectedGroupIds(newSelectedGroupIds);

    // Clear subscription selection if it's no longer valid
    if (selectedSubscriptionId) {
      const validSubscription = subscriptions.find(sub =>
        sub.id === selectedSubscriptionId &&
        (newSelectedGroupIds.length === 0 || newSelectedGroupIds.includes(sub.groupId))
      );
      if (!validSubscription) {
        setSelectedSubscriptionId('');
        setPricePointsByCountry({});
      }
    }
  };

  const fetchPricePoints = async () => {
    if (!selectedSubscriptionId) return;

    setLoading(true);
    setError('');
    setPricePointsByCountry({});

    try {
      const service = createServiceFromEnv();
      const territories = selectedTerritories.length > 0 ? selectedTerritories : undefined;
      const pricePoints = await service.subscriptions.getSubscriptionPricePointsByCountry(
        selectedSubscriptionId,
        territories
      );
      setPricePointsByCountry(pricePoints);
    } catch (err) {
      const errorInfo = parseError(err);
      setError(`${errorInfo.title}: ${errorInfo.message}`);
      setRetryable(errorInfo.retryable);
    } finally {
      setLoading(false);
    }
  };

  if (configError) {
    return (
      <div className="app">
        <header className="app-header">
          <h1>Apple App Store Connect - Price Points</h1>
        </header>
        <main className="app-main">
          <div className="config-error">
            <h2>Configuration Required</h2>
            <p>{configError}</p>
            <div className="config-instructions">
              <h3>Setup Instructions:</h3>
              <ol>
                <li>Go to App Store Connect → Users and Access → Keys</li>
                <li>Create a new API key with appropriate permissions</li>
                <li>Download the .p8 private key file</li>
                <li>Create a .env file in the project root with:</li>
              </ol>
              <pre>
{`APPLE_KEY_ID=your_key_id
APPLE_ISSUER_ID=your_issuer_id
APPLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
your_private_key_content
-----END PRIVATE KEY-----"`}
              </pre>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>Apple App Store Connect - Subscription Price Points</h1>
        <p>Fetch and display subscription price points across different countries</p>
      </header>

      <main className="app-main">
        <div className="filters-section">
          <div className="filters-grid">
            <AppSelector
              apps={apps}
              selectedAppIds={selectedAppIds}
              onSelectionChange={handleAppSelectionChange}
              loading={loading}
              disabled={loading}
            />

            <SubscriptionGroupSelector
              subscriptionGroups={subscriptionGroups}
              selectedGroupIds={selectedGroupIds}
              onSelectionChange={handleGroupSelectionChange}
              selectedAppIds={selectedAppIds}
              loading={loading}
              disabled={loading}
            />

            <TerritorySelector
              selectedTerritories={selectedTerritories}
              onSelectionChange={setSelectedTerritories}
              disabled={loading}
            />
          </div>
        </div>

        <div className="subscription-section">
          <SubscriptionSelector
            subscriptions={filteredSubscriptions}
            selectedSubscriptionId={selectedSubscriptionId}
            onSubscriptionChange={setSelectedSubscriptionId}
            loading={loading}
            onFetchPricePoints={fetchPricePoints}
          />
        </div>

        <PricePointsDisplay
          pricePointsByCountry={pricePointsByCountry}
          loading={loading}
          error={error}
          retryable={retryable}
          onRetry={fetchPricePoints}
        />
      </main>
    </div>
  );
}

export default App;
