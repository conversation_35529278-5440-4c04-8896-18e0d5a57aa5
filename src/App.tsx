import { useState, useEffect } from 'react';
import { PricePointsDisplay } from './components/PricePointsDisplay';
import { SubscriptionSelector } from './components/SubscriptionSelector';
import {
  createServiceFromEnv,
  isAppStoreConnectConfigured
} from './services/appStoreConnect';
import type {
  Subscription,
  PricePointsByCountry,
  TerritoryCode
} from './services/appStoreConnect';
import { parseError } from './utils/errorHandling';
import './App.css';

function App() {
  const [subscriptions, setSubscriptions] = useState<Array<Subscription & { appName: string, groupName: string }>>([]);
  const [selectedSubscriptionId, setSelectedSubscriptionId] = useState<string>('');
  const [selectedTerritories, setSelectedTerritories] = useState<TerritoryCode[]>([]);
  const [pricePointsByCountry, setPricePointsByCountry] = useState<PricePointsByCountry>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [configError, setConfigError] = useState<string>('');
  const [retryable, setRetryable] = useState(false);

  // Check configuration on mount
  useEffect(() => {
    if (!isAppStoreConnectConfigured()) {
      setConfigError(
        'App Store Connect API is not configured. Please set the required environment variables: ' +
        'APPLE_KEY_ID, APPLE_ISSUER_ID, and APPLE_PRIVATE_KEY'
      );
      return;
    }

    // Load subscriptions on mount
    loadSubscriptions();
  }, []);

  const loadSubscriptions = async () => {
    setLoading(true);
    setError('');

    try {
      const service = createServiceFromEnv();
      const result = await service.subscriptions.getAllSubscriptions();
      setSubscriptions(result.subscriptions);
    } catch (err) {
      const errorInfo = parseError(err);
      setError(`${errorInfo.title}: ${errorInfo.message}`);
      setRetryable(errorInfo.retryable);
    } finally {
      setLoading(false);
    }
  };

  const fetchPricePoints = async () => {
    if (!selectedSubscriptionId) return;

    setLoading(true);
    setError('');
    setPricePointsByCountry({});

    try {
      const service = createServiceFromEnv();
      const territories = selectedTerritories.length > 0 ? selectedTerritories : undefined;
      const pricePoints = await service.subscriptions.getSubscriptionPricePointsByCountry(
        selectedSubscriptionId,
        territories
      );
      setPricePointsByCountry(pricePoints);
    } catch (err) {
      const errorInfo = parseError(err);
      setError(`${errorInfo.title}: ${errorInfo.message}`);
      setRetryable(errorInfo.retryable);
    } finally {
      setLoading(false);
    }
  };

  if (configError) {
    return (
      <div className="app">
        <header className="app-header">
          <h1>Apple App Store Connect - Price Points</h1>
        </header>
        <main className="app-main">
          <div className="config-error">
            <h2>Configuration Required</h2>
            <p>{configError}</p>
            <div className="config-instructions">
              <h3>Setup Instructions:</h3>
              <ol>
                <li>Go to App Store Connect → Users and Access → Keys</li>
                <li>Create a new API key with appropriate permissions</li>
                <li>Download the .p8 private key file</li>
                <li>Create a .env file in the project root with:</li>
              </ol>
              <pre>
{`APPLE_KEY_ID=your_key_id
APPLE_ISSUER_ID=your_issuer_id
APPLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
your_private_key_content
-----END PRIVATE KEY-----"`}
              </pre>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>Apple App Store Connect - Subscription Price Points</h1>
        <p>Fetch and display subscription price points across different countries</p>
      </header>

      <main className="app-main">
        <SubscriptionSelector
          subscriptions={subscriptions}
          selectedSubscriptionId={selectedSubscriptionId}
          onSubscriptionChange={setSelectedSubscriptionId}
          selectedTerritories={selectedTerritories}
          onTerritoriesChange={setSelectedTerritories}
          loading={loading}
          onFetchPricePoints={fetchPricePoints}
        />

        <PricePointsDisplay
          pricePointsByCountry={pricePointsByCountry}
          loading={loading}
          error={error}
          retryable={retryable}
          onRetry={fetchPricePoints}
        />
      </main>
    </div>
  );
}

export default App;
