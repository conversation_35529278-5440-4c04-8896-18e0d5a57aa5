import { useState, useEffect } from 'react';
import { SubscriptionsTable } from './components/SubscriptionsTable';
import { AppSelector } from './components/AppSelector';
import { SubscriptionGroupSelector } from './components/SubscriptionGroupSelector';
import { TerritorySelector } from './components/TerritorySelector';
import {
  createServiceFromEnv,
  isAppStoreConnectConfigured
} from './services/appStoreConnect';
import type {
  Subscription,
  TerritoryCode,
  AppStoreApp,
  SubscriptionGroup
} from './services/appStoreConnect';
import { parseError } from './utils/errorHandling';
import './App.css';

interface SubscriptionGroupWithApp extends SubscriptionGroup {
  appName: string;
  appId: string;
}

function App() {
  // Core data state
  const [apps, setApps] = useState<AppStoreApp[]>([]);
  const [subscriptionGroups, setSubscriptionGroups] = useState<SubscriptionGroupWithApp[]>([]);
  const [subscriptions, setSubscriptions] = useState<Array<Subscription & { appName: string, groupName: string, appId: string, groupId: string }>>([]);

  // Filter state with default selections
  const [selectedAppIds, setSelectedAppIds] = useState<string[]>([]);
  const [selectedGroupIds, setSelectedGroupIds] = useState<string[]>([]);
  const [selectedTerritories, setSelectedTerritories] = useState<TerritoryCode[]>(['DE']); // Default to Germany

  // UI state
  const [loading, setLoading] = useState(false);
  const [configError, setConfigError] = useState<string>('');

  // Check configuration on mount
  useEffect(() => {
    if (!isAppStoreConnectConfigured()) {
      setConfigError(
        'App Store Connect API is not configured. Please set the required environment variables: ' +
        'APPLE_KEY_ID, APPLE_ISSUER_ID, and APPLE_PRIVATE_KEY'
      );
      return;
    }

    // Load subscriptions on mount
    loadSubscriptions();
  }, []);

  const loadSubscriptions = async () => {
    setLoading(true);

    try {
      const service = createServiceFromEnv();
      const result = await service.subscriptions.getAllSubscriptions();

      // Set apps data and auto-select first app
      setApps(result.apps);
      if (result.apps.length > 0 && selectedAppIds.length === 0) {
        setSelectedAppIds([result.apps[0].id]);
      }

      // Build subscription groups with app context
      const groupsWithApp: SubscriptionGroupWithApp[] = [];
      for (const app of result.apps) {
        try {
          const groups = await service.subscriptions.getSubscriptionGroups(app.id);
          groups.forEach(group => {
            groupsWithApp.push({
              ...group,
              appName: app.attributes?.name || app.id,
              appId: app.id
            });
          });
        } catch (error) {
          console.warn(`Failed to load groups for app ${app.id}:`, error);
        }
      }
      setSubscriptionGroups(groupsWithApp);

      // Auto-select first group if none selected
      if (groupsWithApp.length > 0 && selectedGroupIds.length === 0) {
        setSelectedGroupIds([groupsWithApp[0].id]);
      }

      // Set subscriptions with enhanced context
      const enhancedSubscriptions = result.subscriptions.map(sub => ({
        ...sub,
        appId: groupsWithApp.find(g => g.attributes.referenceName === sub.groupName)?.appId || '',
        groupId: groupsWithApp.find(g => g.attributes.referenceName === sub.groupName)?.id || ''
      }));
      setSubscriptions(enhancedSubscriptions);
    } catch (err) {
      const errorInfo = parseError(err);
      console.error(`Failed to load subscriptions: ${errorInfo.title}: ${errorInfo.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Filter subscriptions based on selected apps and groups
  const filteredSubscriptions = subscriptions.filter(subscription => {
    const appMatch = selectedAppIds.length === 0 || selectedAppIds.includes(subscription.appId);
    const groupMatch = selectedGroupIds.length === 0 || selectedGroupIds.includes(subscription.groupId);
    return appMatch && groupMatch;
  });

  // Handle filter changes with cascading effects
  const handleAppSelectionChange = (newSelectedAppIds: string[]) => {
    setSelectedAppIds(newSelectedAppIds);

    // Clear group selection if selected apps change
    if (selectedGroupIds.length > 0) {
      const validGroupIds = subscriptionGroups
        .filter(group => newSelectedAppIds.length === 0 || newSelectedAppIds.includes(group.appId))
        .map(group => group.id);
      const filteredGroupIds = selectedGroupIds.filter(id => validGroupIds.includes(id));
      setSelectedGroupIds(filteredGroupIds);
    }
  };

  const handleGroupSelectionChange = (newSelectedGroupIds: string[]) => {
    setSelectedGroupIds(newSelectedGroupIds);
  };

  if (configError) {
    return (
      <div className="app">
        <header className="app-header">
          <h1>Apple App Store Connect - Price Points</h1>
        </header>
        <main className="app-main">
          <div className="config-error">
            <h2>Configuration Required</h2>
            <p>{configError}</p>
            <div className="config-instructions">
              <h3>Setup Instructions:</h3>
              <ol>
                <li>Go to App Store Connect → Users and Access → Keys</li>
                <li>Create a new API key with appropriate permissions</li>
                <li>Download the .p8 private key file</li>
                <li>Create a .env file in the project root with:</li>
              </ol>
              <pre>
{`APPLE_KEY_ID=your_key_id
APPLE_ISSUER_ID=your_issuer_id
APPLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
your_private_key_content
-----END PRIVATE KEY-----"`}
              </pre>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>Apple App Store Connect - Subscription Price Points</h1>
        <p>Fetch and display subscription price points across different countries</p>
      </header>

      <main className="app-main">
        <div className="filters-section">
          <div className="filters-grid">
            <AppSelector
              apps={apps}
              selectedAppIds={selectedAppIds}
              onSelectionChange={handleAppSelectionChange}
              loading={loading}
              disabled={loading}
            />

            <SubscriptionGroupSelector
              subscriptionGroups={subscriptionGroups}
              selectedGroupIds={selectedGroupIds}
              onSelectionChange={handleGroupSelectionChange}
              selectedAppIds={selectedAppIds}
              loading={loading}
              disabled={loading}
            />

            <TerritorySelector
              selectedTerritories={selectedTerritories}
              onSelectionChange={setSelectedTerritories}
              disabled={loading}
            />
          </div>
        </div>

        <div className="subscription-section">
          <SubscriptionsTable
            subscriptions={filteredSubscriptions}
            selectedTerritories={selectedTerritories}
            loading={loading}
          />
        </div>


      </main>
    </div>
  );
}

export default App;
