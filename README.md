# Apple App Store Connect API - Price Store

A React + TypeScript application for fetching and displaying subscription price points from Apple's App Store Connect API across different countries and territories.

## Features

- 🔐 **JWT Authentication** - Secure authentication with Apple App Store Connect API
- 📊 **Price Points Retrieval** - Fetch subscription pricing data for multiple countries
- 🌍 **Multi-Territory Support** - View pricing across different countries and currencies
- 🔄 **Error Handling** - Comprehensive error handling with retry functionality
- 📱 **Responsive UI** - Clean, responsive interface for viewing pricing data
- ⚡ **Real-time Data** - Live data from Apple's API with proper token management

## Prerequisites

Before you begin, you need:

1. **Apple Developer Account** with App Store Connect access
2. **App Store Connect API Key** with appropriate permissions
3. **Node.js** (version 18 or higher)
4. **pnpm** (or npm/yarn)

## Setup Instructions

### 1. Create App Store Connect API Key

1. Go to [App Store Connect](https://appstoreconnect.apple.com)
2. Navigate to **Users and Access** → **Keys**
3. Click **Generate API Key** or the **+** button
4. Fill in the key details:
   - **Name**: Give your key a descriptive name
   - **Access**: Select appropriate access level (Admin or Developer)
   - **Roles**: Ensure the key has access to subscription management
5. Click **Generate**
6. **Download the .p8 file** immediately (you can only download it once)
7. Note down the **Key ID** and **Issuer ID**

### 2. Install Dependencies

```bash
# Clone the repository
git clone <repository-url>
cd price-store

# Install dependencies
pnpm install
```

### 3. Configure Environment Variables

1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Edit `.env` and add your App Store Connect API credentials:
```env
# Your App Store Connect API Key ID (10 characters, e.g., "ABC123DEFG")
APPLE_KEY_ID=your_key_id_here

# Your App Store Connect Issuer ID (UUID format)
APPLE_ISSUER_ID=12345678-1234-1234-1234-123456789012

# Your App Store Connect Private Key (contents of the .p8 file)
APPLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQg...
-----END PRIVATE KEY-----"
```

**Important Notes:**
- Include the full PEM format with headers and footers
- Keep the private key secure and never commit it to version control
- The `.env` file is already in `.gitignore`

### 4. Run the Application

```bash
# Start the development server
pnpm dev
```

The application will be available at `http://localhost:5173`

## Usage

1. **Launch the application** - The app will automatically validate your API configuration
2. **Select a subscription** - Choose from the dropdown of available subscriptions
3. **Filter territories** (optional) - Select specific countries/territories or leave empty for all
4. **Fetch price points** - Click the "Fetch Price Points" button
5. **View results** - Browse pricing data organized by country/territory

## API Endpoints Used

- `GET /v1/subscriptions` - Retrieve available subscriptions
- `GET /v1/subscriptions/{id}/pricePoints` - Fetch price points for a subscription

## Error Handling

The application handles various error scenarios:

- **401 NOT_AUTHORIZED** - Invalid or expired API credentials
- **403 FORBIDDEN** - Insufficient permissions
- **404 NOT_FOUND** - Subscription not found
- **429 RATE_LIMITED** - Too many requests
- **Network errors** - Connection issues

Each error includes helpful suggestions for resolution.

## Development

### Project Structure

```
src/
├── components/           # React components
│   ├── PricePointsDisplay.tsx
│   └── SubscriptionSelector.tsx
├── services/            # API services
│   └── appStoreConnect/
│       ├── auth.ts      # JWT authentication
│       ├── client.ts    # HTTP client
│       ├── config.ts    # Configuration management
│       ├── subscriptions.ts # Subscription services
│       ├── types.ts     # TypeScript types
│       └── index.ts     # Main exports
├── utils/               # Utility functions
│   ├── errorHandling.ts # Error parsing and handling
│   └── validation.ts    # Input validation
└── test/               # Test utilities
    └── testAuth.ts     # Authentication testing
```

### Testing

You can test your API configuration using the built-in test utilities:

```typescript
// In browser console
testAppStoreConnect.runAllTests('your-subscription-id');
```

## Troubleshooting

### Common Issues

1. **401 NOT_AUTHORIZED Error**
   - Verify your Key ID, Issuer ID, and Private Key are correct
   - Ensure the private key is in proper PEM format
   - Check that your API key hasn't expired

2. **Private Key Format Issues**
   - Make sure to include the full PEM headers and footers
   - Verify there are no extra spaces or characters
   - The key should be exactly as downloaded from Apple

3. **No Subscriptions Found**
   - Ensure your app has subscription products configured
   - Verify your API key has the necessary permissions
   - Check that subscriptions are in the correct state

4. **Network/CORS Issues**
   - The API calls are made server-side to avoid CORS issues
   - Ensure your network allows HTTPS connections to api.appstoreconnect.apple.com

### Getting Help

If you encounter issues:

1. Check the browser console for detailed error messages
2. Verify your API credentials are correctly formatted
3. Test your configuration using the built-in test utilities
4. Refer to [Apple's App Store Connect API documentation](https://developer.apple.com/documentation/appstoreconnectapi)

## Security Notes

- Never commit your `.env` file or API credentials to version control
- Store your private key securely
- Rotate your API keys regularly
- Use environment-specific configurations for different deployments

## License

This project is licensed under the MIT License.
