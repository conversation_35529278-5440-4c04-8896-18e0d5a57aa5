# Apple App Store Connect API Configuration
# Get these values from your Apple Developer account:
# 1. Go to App Store Connect > Users and Access > Keys
# 2. Create a new API key with appropriate permissions
# 3. Download the .p8 private key file

# Your App Store Connect API Key ID (e.g., "ABC123DEFG")
APPLE_KEY_ID=

# Your App Store Connect Issuer ID (e.g., "********-1234-1234-1234-************")
APPLE_ISSUER_ID=

# Your App Store Connect Private Key (contents of the .p8 file)
# Include the full PEM format with headers:
# -----BEGIN PRIVATE KEY-----
# [key content]
# -----END PRIVATE KEY-----
APPLE_PRIVATE_KEY=

# Optional: Default subscription ID for testing
DEFAULT_SUBSCRIPTION_ID=
