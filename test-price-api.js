#!/usr/bin/env node

/**
 * Test script to verify our enhanced price fetching implementation
 * This simulates the browser environment and tests our API calls
 */

import { createServiceFromEnv } from './src/services/appStoreConnect/index.js';

async function testPriceFetching() {
  console.log('🧪 Testing Enhanced Price Fetching Implementation\n');
  
  try {
    const service = createServiceFromEnv();
    console.log('✅ Service created successfully');
    
    // Test 1: Get all subscriptions to find a test subscription
    console.log('\n📋 Step 1: Getting subscriptions...');
    const subscriptionsResult = await service.subscriptions.getAllSubscriptions();
    console.log(`✅ Found ${subscriptionsResult.subscriptions.length} subscriptions`);
    
    if (subscriptionsResult.subscriptions.length === 0) {
      console.log('❌ No subscriptions found to test with');
      return;
    }
    
    const testSubscription = subscriptionsResult.subscriptions[0];
    console.log(`🎯 Testing with subscription: ${testSubscription.id} (${testSubscription.name})`);
    
    // Test 2: Try subscription-specific price points (Strategy 1)
    console.log('\n🔄 Step 2: Testing subscription-specific price points...');
    try {
      const subscriptionPrices = await service.subscriptions.getSubscriptionPricePointsByCountry(
        testSubscription.id,
        ['DE'] // Germany for EUR
      );
      console.log('✅ Subscription-specific price points successful');
      console.log('📊 Response keys:', Object.keys(subscriptionPrices));
      console.log('📊 Territory data:', subscriptionPrices['DEU'] ? 'Found DEU data' : 'No DEU data');
      
      if (subscriptionPrices['DEU']?.pricePoints?.length > 0) {
        const price = subscriptionPrices['DEU'].pricePoints[0].customerPrice;
        console.log(`💰 Found price: ${price}`);
      }
    } catch (error) {
      console.log('⚠️ Subscription-specific price points failed:', error.message);
      
      // Test 3: Try app-level price points (Strategy 2)
      console.log('\n🔄 Step 3: Testing app-level price points fallback...');
      
      // Find the app ID for this subscription
      const appId = subscriptionsResult.apps.find(app => 
        app.attributes?.name === testSubscription.appName
      )?.id;
      
      if (appId) {
        console.log(`🎯 Found app ID: ${appId}`);
        try {
          const appPriceResponse = await service.client.get(
            `/apps/${appId}/pricePoints`,
            {
              'filter[territory]': 'DEU',
              'include': 'territory',
              'fields[subscriptionPricePoints]': 'customerPrice,proceeds',
              'fields[territories]': 'currency',
              'limit': 1
            }
          );
          console.log('✅ App-level price points successful');
          console.log('📊 App price response:', appPriceResponse);
        } catch (appError) {
          console.log('❌ App-level price points also failed:', appError.message);
        }
      } else {
        console.log('❌ Could not find app ID for fallback test');
      }
    }
    
    console.log('\n🎉 Test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response?.data?.errors) {
      console.error('📋 API Errors:');
      error.response.data.errors.forEach((err, i) => {
        console.error(`   ${i + 1}. ${err.code}: ${err.title}`);
      });
    }
  }
}

testPriceFetching();