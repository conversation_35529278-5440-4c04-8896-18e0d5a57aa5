#!/usr/bin/env node

/**
 * Comprehensive API Endpoint Testing Script
 * Tests Apple App Store Connect API endpoints with configured credentials
 */

import { SignJWT, importPKCS8 } from 'jose';
import axios from 'axios';

// Configuration from your setup
const CONFIG = {
  keyId: 'K537536885',
  issuerId: '8d28e2ea-bda8-48ce-ba39-3a7437716759',
  privateKey: `-----BEGIN PRIVATE KEY-----
MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgNXbG/76U2ryPyts5
meU30gefhzWjVA1JSVGMs4QZdGWgCgYIKoZIzj0DAQehRANCAASqAsGUJqF6eG4Y
5zPCuHoA5bm93DBiAWCdgEEPXILr/lyo35kcX0dsdAyVvn+fBlB1Yp0rgPNF2ONZ
6qtW+xOh
-----<PERSON><PERSON> PRIVATE KEY-----`
};

const BASE_URL = 'https://api.appstoreconnect.apple.com/v1';

// Test results tracking
const testResults = {
  authToken: { status: 'pending', message: '', details: null },
  subscriptions: { status: 'pending', message: '', details: null },
  pricePoints: { status: 'pending', message: '', details: null }
};

/**
 * Generate JWT token for authentication
 */
async function generateJWTToken() {
  try {
    console.log('🔐 Testing JWT Token Generation...');
    
    const now = Math.floor(Date.now() / 1000);
    const expiry = now + (20 * 60); // 20 minutes

    // Import the private key
    const privateKey = await importPKCS8(CONFIG.privateKey, 'ES256');

    // Create and sign the JWT
    const token = await new SignJWT({
      iss: CONFIG.issuerId,
      iat: now,
      exp: expiry,
      aud: 'appstoreconnect-v1'
    })
      .setProtectedHeader({
        alg: 'ES256',
        kid: CONFIG.keyId,
        typ: 'JWT'
      })
      .sign(privateKey);

    testResults.authToken = {
      status: 'success',
      message: 'JWT token generated successfully',
      details: {
        tokenLength: token.length,
        expiresIn: '20 minutes',
        algorithm: 'ES256'
      }
    };

    console.log('✅ JWT Token Generation: SUCCESS');
    console.log(`   Token Length: ${token.length} characters`);
    console.log(`   Expires: ${new Date(expiry * 1000).toISOString()}`);
    
    return token;
  } catch (error) {
    testResults.authToken = {
      status: 'error',
      message: `JWT generation failed: ${error.message}`,
      details: { error: error.message }
    };
    
    console.log('❌ JWT Token Generation: FAILED');
    console.log(`   Error: ${error.message}`);
    throw error;
  }
}

/**
 * Test subscriptions endpoint
 */
async function testSubscriptionsEndpoint(token) {
  try {
    console.log('\n📱 Testing Subscriptions Endpoint...');
    
    const response = await axios.get(`${BASE_URL}/subscriptions`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      params: {
        limit: 10,
        'fields[subscriptions]': 'name,productId,familySharable,state,subscriptionPeriod'
      },
      timeout: 30000
    });

    const subscriptions = response.data.data || [];
    
    testResults.subscriptions = {
      status: 'success',
      message: `Found ${subscriptions.length} subscriptions`,
      details: {
        count: subscriptions.length,
        subscriptions: subscriptions.map(sub => ({
          id: sub.id,
          name: sub.attributes?.name,
          productId: sub.attributes?.productId,
          state: sub.attributes?.state
        }))
      }
    };

    console.log('✅ Subscriptions Endpoint: SUCCESS');
    console.log(`   Found: ${subscriptions.length} subscriptions`);
    
    if (subscriptions.length > 0) {
      console.log('   Sample subscriptions:');
      subscriptions.slice(0, 3).forEach((sub, index) => {
        console.log(`   ${index + 1}. ${sub.attributes?.name || 'Unnamed'} (${sub.attributes?.productId || sub.id})`);
      });
    }

    return subscriptions;
  } catch (error) {
    const errorMessage = error.response?.data?.errors?.[0]?.detail || error.message;
    const statusCode = error.response?.status;
    
    testResults.subscriptions = {
      status: 'error',
      message: `Subscriptions API failed: ${errorMessage}`,
      details: { 
        statusCode,
        error: errorMessage,
        fullError: error.response?.data
      }
    };

    console.log('❌ Subscriptions Endpoint: FAILED');
    console.log(`   Status: ${statusCode}`);
    console.log(`   Error: ${errorMessage}`);
    
    throw error;
  }
}

/**
 * Test price points endpoint
 */
async function testPricePointsEndpoint(token, subscriptions) {
  try {
    console.log('\n💰 Testing Price Points Endpoint...');
    
    if (!subscriptions || subscriptions.length === 0) {
      throw new Error('No subscriptions available to test price points');
    }

    const testSubscription = subscriptions[0];
    console.log(`   Testing with subscription: ${testSubscription.attributes?.name || testSubscription.id}`);

    const response = await axios.get(`${BASE_URL}/subscriptions/${testSubscription.id}/pricePoints`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      params: {
        limit: 20,
        'fields[subscriptionPricePoints]': 'customerPrice,proceeds,proceedsDate',
        'fields[territories]': 'currency',
        'fields[subscriptions]': 'name,productId',
        include: 'territory,subscription'
      },
      timeout: 30000
    });

    const pricePoints = response.data.data || [];
    const included = response.data.included || [];
    
    // Process territories and currencies
    const territories = included
      .filter(item => item.type === 'territories')
      .reduce((acc, territory) => {
        acc[territory.id] = territory.attributes?.currency;
        return acc;
      }, {});

    const territoryCount = Object.keys(territories).length;
    const sampleTerritories = Object.entries(territories).slice(0, 5);

    testResults.pricePoints = {
      status: 'success',
      message: `Found ${pricePoints.length} price points across ${territoryCount} territories`,
      details: {
        pricePointsCount: pricePoints.length,
        territoriesCount: territoryCount,
        sampleTerritories: sampleTerritories.map(([id, currency]) => ({ id, currency })),
        samplePricePoints: pricePoints.slice(0, 3).map(pp => ({
          customerPrice: pp.attributes?.customerPrice,
          proceeds: pp.attributes?.proceeds,
          territoryId: pp.relationships?.territory?.data?.id
        }))
      }
    };

    console.log('✅ Price Points Endpoint: SUCCESS');
    console.log(`   Found: ${pricePoints.length} price points`);
    console.log(`   Territories: ${territoryCount} countries/regions`);
    
    if (sampleTerritories.length > 0) {
      console.log('   Sample territories:');
      sampleTerritories.forEach(([id, currency]) => {
        console.log(`   - ${id}: ${currency}`);
      });
    }

    return { pricePoints, territories };
  } catch (error) {
    const errorMessage = error.response?.data?.errors?.[0]?.detail || error.message;
    const statusCode = error.response?.status;
    
    testResults.pricePoints = {
      status: 'error',
      message: `Price Points API failed: ${errorMessage}`,
      details: { 
        statusCode,
        error: errorMessage,
        fullError: error.response?.data
      }
    };

    console.log('❌ Price Points Endpoint: FAILED');
    console.log(`   Status: ${statusCode}`);
    console.log(`   Error: ${errorMessage}`);
    
    throw error;
  }
}

/**
 * Print comprehensive test results
 */
function printTestSummary() {
  console.log('\n' + '='.repeat(60));
  console.log('📊 COMPREHENSIVE TEST RESULTS SUMMARY');
  console.log('='.repeat(60));

  const tests = [
    { name: 'JWT Authentication', result: testResults.authToken },
    { name: 'Subscriptions API', result: testResults.subscriptions },
    { name: 'Price Points API', result: testResults.pricePoints }
  ];

  tests.forEach(test => {
    const status = test.result.status === 'success' ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${test.name}: ${test.result.message}`);
    
    if (test.result.details && test.result.status === 'success') {
      Object.entries(test.result.details).forEach(([key, value]) => {
        if (typeof value === 'object' && Array.isArray(value)) {
          console.log(`     ${key}: ${value.length} items`);
        } else if (typeof value === 'object') {
          console.log(`     ${key}: ${JSON.stringify(value)}`);
        } else {
          console.log(`     ${key}: ${value}`);
        }
      });
    }
  });

  const allPassed = tests.every(test => test.result.status === 'success');
  
  console.log('\n' + '='.repeat(60));
  if (allPassed) {
    console.log('🎉 ALL TESTS PASSED! Your Apple App Store Connect API integration is working correctly.');
    console.log('✅ You can now use the web application to fetch subscription price points.');
  } else {
    console.log('⚠️  Some tests failed. Please check the errors above and verify your configuration.');
  }
  console.log('='.repeat(60));
}

/**
 * Main test execution
 */
async function runAllTests() {
  console.log('🚀 Starting Apple App Store Connect API Endpoint Tests...');
  console.log('='.repeat(60));

  try {
    // Test 1: JWT Token Generation
    const token = await generateJWTToken();

    // Test 2: Subscriptions Endpoint
    const subscriptions = await testSubscriptionsEndpoint(token);

    // Test 3: Price Points Endpoint
    await testPricePointsEndpoint(token, subscriptions);

  } catch (error) {
    console.log(`\n💥 Test execution stopped due to error: ${error.message}`);
  }

  // Print comprehensive summary
  printTestSummary();
}

// Execute tests
runAllTests().catch(console.error);
