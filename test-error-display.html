<!DOCTYPE html>
<html>
<head>
    <title>Error Display Test</title>
    <style>
        .error-text {
            color: #dc2626;
            font-size: 12px;
            font-weight: 500;
            cursor: help;
            border-bottom: 1px dotted #dc2626;
            padding: 2px 4px;
            border-radius: 3px;
            background: #fef2f2;
            display: inline-block;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin: 5px;
        }
        .error-text:hover {
            background: #fee2e2;
            border-bottom: 1px solid #dc2626;
        }
    </style>
</head>
<body>
    <h2>Enhanced Error Display Examples</h2>
    
    <h3>Rate Limit Error (429):</h3>
    <span class="error-text" title="RATE_LIMIT_EXCEEDED: The request rate limit has been reached.">
        RATE_LIMIT_EXCEEDED: The request rate limit has been reached.
    </span>
    
    <h3>Authentication Error (401):</h3>
    <span class="error-text" title="NOT_AUTHORIZED: Authentication credentials are missing or invalid.">
        NOT_AUTHORIZED: Authentication credentials are missing or invalid.
    </span>
    
    <h3>HTTP Error:</h3>
    <span class="error-text" title="HTTP 500">HTTP 500</span>
    
    <h3>Network Error:</h3>
    <span class="error-text" title="Network Error">Network Error</span>
    
    <p><strong>Features:</strong></p>
    <ul>
        <li>✅ Shows error code and title from API response</li>
        <li>✅ Truncates long error messages with ellipsis</li>
        <li>✅ Full error details on hover (tooltip)</li>
        <li>✅ Styled with red background and dotted border</li>
        <li>✅ Hover effect for better UX</li>
    </ul>
</body>
</html>
