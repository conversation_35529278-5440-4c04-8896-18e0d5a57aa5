# Troubleshooting Guide

## Common Issues and Solutions

### 1. Authentication Errors

#### 401 NOT_AUTHORIZED
**Symptoms**: "Authentication Failed" error, 401 status code

**Possible Causes**:
- Invalid API credentials
- Expired API key
- Incorrect private key format
- Wrong Key ID or Issuer ID

**Solutions**:
1. **Verify Credentials**:
   ```bash
   # Check your .env file
   cat .env
   ```
   
2. **Validate Key Format**:
   - Private key must include headers: `-----BEGIN PRIVATE KEY-----`
   - Private key must include footers: `-----END PRIVATE KEY-----`
   - No extra spaces or characters

3. **Check API Key Status**:
   - Go to App Store Connect → Users and Access → Keys
   - Verify key is still active and not expired

4. **Test Configuration**:
   ```typescript
   // In browser console
   testAppStoreConnect.testAuthentication();
   ```

#### 403 FORBIDDEN
**Symptoms**: "Access Forbidden" error

**Solutions**:
1. **Check API Key Permissions**:
   - Ensure key has <PERSON><PERSON> or <PERSON><PERSON>per access
   - Verify access to subscription management
   
2. **Verify Account Access**:
   - Confirm you have access to the subscription
   - Check if subscription belongs to your account

### 2. Configuration Issues

#### Missing Environment Variables
**Symptoms**: "Configuration Required" message on app load

**Solutions**:
1. **Create .env file**:
   ```bash
   cp .env.example .env
   ```

2. **Set all required variables**:
   ```env
   APPLE_KEY_ID=your_key_id
   APPLE_ISSUER_ID=your_issuer_id
   APPLE_PRIVATE_KEY="your_private_key"
   ```

3. **Restart development server**:
   ```bash
   pnpm dev
   ```

#### Invalid Private Key Format
**Symptoms**: "Private key content does not appear to be valid base64"

**Solutions**:
1. **Check PEM Format**:
   ```
   -----BEGIN PRIVATE KEY-----
   MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQg...
   -----END PRIVATE KEY-----
   ```

2. **Remove Extra Characters**:
   - No spaces before/after headers
   - No extra line breaks
   - Exactly as downloaded from Apple

3. **Escape in .env**:
   ```env
   APPLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
   MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQg...
   -----END PRIVATE KEY-----"
   ```

### 3. API Response Issues

#### No Subscriptions Found
**Symptoms**: Empty subscription dropdown

**Possible Causes**:
- No subscription products configured
- Subscriptions in wrong state
- Insufficient permissions

**Solutions**:
1. **Check App Store Connect**:
   - Go to your app → Subscriptions
   - Verify subscriptions exist and are configured

2. **Check Subscription State**:
   - Subscriptions must be in appropriate state
   - Not all states may be returned by API

3. **Verify API Permissions**:
   - Ensure API key can access subscription data

#### 404 Subscription Not Found
**Symptoms**: "Resource Not Found" when fetching price points

**Solutions**:
1. **Verify Subscription ID**:
   - Check the subscription ID is correct
   - Ensure subscription exists in your account

2. **Check Subscription Status**:
   - Subscription must be in valid state
   - Some states may not have price points

#### Rate Limiting (429)
**Symptoms**: "Rate Limit Exceeded" error

**Solutions**:
1. **Wait and Retry**:
   - Wait 1-2 minutes before retrying
   - Implement exponential backoff

2. **Reduce Request Frequency**:
   - Don't make rapid successive requests
   - Cache results when possible

### 4. Network Issues

#### Connection Timeout
**Symptoms**: "Network Error" or timeout messages

**Solutions**:
1. **Check Internet Connection**:
   ```bash
   ping api.appstoreconnect.apple.com
   ```

2. **Verify Firewall Settings**:
   - Ensure HTTPS (443) is allowed
   - Check corporate firewall rules

3. **Try Different Network**:
   - Test on different internet connection
   - Use mobile hotspot to isolate network issues

#### CORS Issues
**Symptoms**: CORS errors in browser console

**Note**: This shouldn't occur with current implementation, but if it does:

**Solutions**:
1. **Use Development Server**:
   ```bash
   pnpm dev
   ```

2. **Check Vite Configuration**:
   - Ensure proxy is configured correctly
   - Verify CORS headers

### 5. Build and Development Issues

#### TypeScript Errors
**Symptoms**: Build fails with TypeScript errors

**Solutions**:
1. **Check Type Imports**:
   ```typescript
   import type { Subscription } from './types';
   ```

2. **Update Dependencies**:
   ```bash
   pnpm update
   ```

3. **Clear Cache**:
   ```bash
   rm -rf node_modules/.cache
   pnpm install
   ```

#### Module Resolution Errors
**Symptoms**: "Cannot resolve module" errors

**Solutions**:
1. **Reinstall Dependencies**:
   ```bash
   rm -rf node_modules pnpm-lock.yaml
   pnpm install
   ```

2. **Check Import Paths**:
   - Verify relative paths are correct
   - Ensure file extensions match

### 6. Runtime Errors

#### JWT Generation Fails
**Symptoms**: "Failed to generate JWT token" error

**Solutions**:
1. **Check Private Key**:
   - Verify key is in PKCS#8 format
   - Ensure key matches the Key ID

2. **Browser Compatibility**:
   - Use modern browser with WebCrypto support
   - Check browser console for detailed errors

3. **Test Key Manually**:
   ```typescript
   // In browser console
   testAppStoreConnect.testAuthentication();
   ```

## Debugging Tools

### Browser Console Testing
```javascript
// Test authentication
testAppStoreConnect.testAuthentication();

// Test price points (replace with real subscription ID)
testAppStoreConnect.testPricePointsRetrieval('your-subscription-id');

// Run all tests
testAppStoreConnect.runAllTests('your-subscription-id');
```

### Network Debugging
1. **Open Browser DevTools** (F12)
2. **Go to Network tab**
3. **Filter by "api.appstoreconnect.apple.com"**
4. **Check request/response details**

### Configuration Validation
```typescript
import { validateAppStoreConnectConfig } from './utils/validation';

const result = validateAppStoreConnectConfig({
  keyId: 'your-key-id',
  issuerId: 'your-issuer-id',
  privateKey: 'your-private-key'
});

console.log('Validation result:', result);
```

## Getting Additional Help

### Apple Resources
- [App Store Connect API Documentation](https://developer.apple.com/documentation/appstoreconnectapi)
- [Apple Developer Forums](https://developer.apple.com/forums/)
- [App Store Connect Help](https://help.apple.com/app-store-connect/)

### System Status
- [Apple Developer System Status](https://developer.apple.com/system-status/)
- Check if App Store Connect API is experiencing issues

### Log Analysis
1. **Check Browser Console** for JavaScript errors
2. **Check Network Tab** for failed requests
3. **Enable Verbose Logging** in development

### Contact Information
If you continue experiencing issues:
1. Check this troubleshooting guide first
2. Search Apple Developer Forums
3. Create a minimal reproduction case
4. Include error messages and configuration (without sensitive data)
