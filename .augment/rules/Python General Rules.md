---
type: "manual"
---

## General Python Development Workflow Rules

These 12 mandatory development workflow rules must be followed throughout the entire application development process. They emphasize code quality, consistency, performance, and effective communication, leveraging Pythonic principles and common libraries.

-----

### Rule 1: Interactive Feedback & Communication Protocol

**ALWAYS** use a designated communication channel or tool (e.g., a project management system, code review platform like GitHub/GitLab, or internal communication tools) after completing any significant development milestone (module implementation, API endpoint completion, feature addition, or bug fix).

  * **Provide comprehensive summaries** including: specific work completed, current implementation status, files modified, next planned steps, and any technical blockers encountered.
  * **WAIT for explicit stakeholder/team approval** before proceeding with major architectural changes, new feature implementations, or significant data model modifications.
  * **Include relevant project context** (e.g., repository links, ticket IDs, or links to related documentation) and detailed progress summaries in all feedback requests.
  * **Establish clear expectations for response times** (e.g., a default timeout for critical feedback).

-----

### Rule 2: Development Server/Process Management

**BEFORE** starting any new development server or background process, **ALWAYS** check for existing running processes that might conflict with the desired port or resource (e.g., `lsof -i:<PORT>` on Unix-like systems, or process management tools for specific services).

  * **ONLY launch a new process/server** if no existing conflict is found or if the existing process has been gracefully terminated.
  * This prevents resource conflicts and ensures development session continuity.

-----

### Rule 3: Library & Documentation Context Acquisition

**ALWAYS** consult official documentation or a centralized knowledge base before integrating or utilizing new libraries, frameworks, or complex features.

  * **Focus documentation requests** on specific topics relevant to the current task (e.g., 'API usage', 'database interactions', 'concurrency', 'testing frameworks', 'security best practices').
  * **Be mindful of information overload**; aim for concise and relevant documentation retrieval.
  * Use library documentation to understand proper usage patterns, best practices, and potential pitfalls before implementing features with external libraries (e.g., FastAPI, Django, SQLAlchemy, Celery, requests, Pytest).

-----

### Rule 4: Structured Planning & Implementation

**ALWAYS** adopt a structured approach for all code planning, analysis, and implementation tasks. Leverage design patterns and architectural principles.

  * **Begin each development session** with a clear understanding of the task objectives and how they fit into the larger project.
  * **Before making any code changes, reflect** on whether your proposed changes align with project goals, existing architecture, and design specifications.
  * **Document important context, progress updates, and key implementation decisions.** This can be in a dedicated `README.md`, developer logs, or design documents.
  * **Utilize IDE features and refactoring tools** for precise code modifications over manual file editing where possible.
  * **Review your work against the initial task definition** before marking it as complete, potentially using a checklist.

-----

### Rule 5: Code Quality & Type Safety Assurance

**ALWAYS** run static analysis tools (e.g., MyPy for type checking, Flake8/Black/Ruff for linting and formatting) after making any significant code changes to modules or functions.

  * **Fix ALL type errors** reported by MyPy, ensuring proper type hints are used throughout the codebase, especially for function signatures, class attributes, and Pydantic models.
  * **Resolve ALL linting errors and adhere to code formatting standards** (e.g., using Black for automatic formatting).
  * **Continuously monitor application logs** during development.
  * **IMMEDIATELY address** any runtime errors, warnings, or unexpected behavior that appear in development logs.
  * **Leverage decorators** for common code quality and logging concerns (e.g., `@logger.log_function_call`, `@time_it` for performance).

-----

### Rule 6: API/Interface Consistency & Data Alignment

**ALWAYS** define clear and consistent API interfaces, data models, and request/response structures.

  * **PRIORITIZE using Pydantic models** for all incoming request bodies, outgoing response bodies, and internal data structures where strict validation is required. This ensures data integrity and provides automatic documentation for APIs.

    ```python
    from pydantic import BaseModel, Field
    from enum import Enum

    class Status(str, Enum):
        PENDING = "pending"
        COMPLETED = "completed"
        FAILED = "failed"

    class TaskCreate(BaseModel):
        title: str = Field(..., min_length=3, max_length=100)
        description: str | None = None

    class TaskResponse(TaskCreate):
        id: int
        status: Status = Status.PENDING
        created_at: datetime
    ```

  * **Adhere to a defined naming convention** (e.g., PEP 8 for Python code, RESTful principles for APIs).

  * **Reuse common data models** to prevent duplication and ensure consistency across different parts of the application.

  * **Verify API behavior** using integration tests and tools like Postman or Insomnia.

-----

### Rule 7: Development Environment Monitoring & Control

**MAINTAIN awareness of your development environment's status.**

  * **Track process health, resource utilization (CPU, memory), and log outputs.**
  * **RESPOND promptly** to dependency conflicts, configuration errors, and platform-specific issues.
  * **Be prepared to manage processes effectively** (e.g., killing stuck processes, restarting services for fresh builds or configuration changes).

-----

### Rule 8: Concurrency & Asynchronous Programming Standards

**ALWAYS** choose appropriate concurrency models for the task at hand (e.g., `asyncio` for I/O-bound operations, `multiprocessing` for CPU-bound tasks).

  * **Leverage `asyncio`** for building high-performance, non-blocking I/O operations, especially in web frameworks like FastAPI.
  * **Use `async`/`await` correctly** to ensure proper asynchronous flow.
  * **Be mindful of the GIL** (Global Interpreter Lock) when dealing with CPU-bound tasks and consider `multiprocessing` for true parallelism.
  * **Consider using decorators** to manage asynchronous tasks or wrap blocking calls (e.g., `@sync_to_async` in Django ORM contexts, or custom `@run_in_threadpool`).

-----

### Rule 9: Configuration Management & Environment Variables

**Manage application configuration effectively.**

  * **Use environment variables** for sensitive information (e.g., API keys, database credentials) and environment-specific settings.

  * **Load configuration securely** using libraries like `python-dotenv` for local development.

  * **Define configuration schemas with Pydantic Settings** (`pydantic-settings` library) for robust, type-checked configuration loading from environment variables, `.env` files, or other sources.

    ```python
    from pydantic_settings import BaseSettings, SettingsConfigDict

    class Settings(BaseSettings):
        app_name: str = "My Awesome App"
        database_url: str
        debug_mode: bool = False

        model_config = SettingsConfigDict(env_file='.env', extra='ignore')

    settings = Settings()
    ```

  * **Avoid hardcoding sensitive data** directly in the codebase.

-----

### Rule 10: Performance Optimization

**Prioritize application performance.**

  * **MINIMIZE unnecessary database queries**; leverage ORM optimizations, caching, and efficient query patterns.
  * **Optimize I/O operations** by using asynchronous programming where beneficial.
  * **Profile code** using tools like `cProfile` or more advanced profilers to identify bottlenecks.
  * **Implement caching strategies** (e.g., Redis, Memcached) for frequently accessed data.
  * **Use decorators** for performance measurement (e.g., `@timeit`) or caching (`@functools.lru_cache`).
  * **Consider background tasks** (e.g., with Celery or RQ) for long-running or non-critical operations.
  * **Optimize data structures and algorithms** for efficiency.

-----

### Rule 11: State Management & Data Flow Architecture

**Define clear state management and data flow patterns.**

  * **Use Pydantic models** to strictly define the schema and types of all data moving through your application (e.g., request/response models, database models, internal DTOs).
  * **Maintain data immutability** where appropriate to prevent unexpected side effects, especially for models passed between functions.
  * **For complex application state**, design a clear data flow, potentially using patterns like CQRS (Command Query Responsibility Segregation) or event-driven architectures.
  * **Handle URL parameters and routing state** consistently within your chosen web framework (e.g., FastAPI's Path/Query parameters, Django's URL dispatchers).

-----

### Rule 12: Validation & Robust Error Handling

**Use Pydantic for robust runtime data validation, API request/response validation, and configuration validation.**

  * **PRIORITIZE comprehensive error handling and edge case management:**
      * Handle error conditions at the beginning of functions using early returns or guard clauses.
      * Use the early return pattern to avoid deeply nested conditional statements.
      * Avoid unnecessary `else` statements; prefer the `if-return` pattern for cleaner code flow.
  * **Implement centralized error handling mechanisms** within your web framework (e.g., FastAPI's `ExceptionHandler`, Django's custom exception middleware).
  * **Provide meaningful, user-friendly error messages** for API consumers.
  * **Log errors appropriately** (including stack traces) for debugging while protecting sensitive user data.
  * **Use custom exception classes** for specific error types to improve clarity and allow for fine-grained error handling.
  * **Leverage decorators** for common error handling patterns, such as `@retry` for transient failures or `@catch_exceptions` to wrap functions with a global error handler.

-----