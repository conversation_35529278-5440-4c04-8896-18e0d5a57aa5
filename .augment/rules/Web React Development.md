---
type: "manual"
---

## General TypeScript & React Development Workflow Rules

These 12 mandatory development workflow rules must be followed throughout the entire application development process. They emphasize code quality, consistency, performance, and effective communication across both web and app platforms using the React tech stack.

-----

### Rule 1: Interactive Feedback & Communication Protocol

**Always** use a designated communication channel or tool (e.g., a shared project management tool, code review system, or a custom "interactive feedback" tool if available) after completing any significant development milestone (component implementation, screen/page completion, feature addition, or bug fix).

  * **Provide comprehensive summaries** including: specific work completed, current implementation status, files modified, next planned steps, and any technical blockers encountered.
  * **Wait for explicit stakeholder/team approval** before proceeding with major architectural changes, new feature implementations, or design system modifications.
  * **Include relevant project context** (e.g., absolute project directory path if applicable in your environment, ticket IDs, or links to related documentation) and detailed progress summaries in all feedback requests.
  * **Establish clear expectations for response times** (e.g., a default timeout of 600 seconds for critical feedback).

-----

### Rule 2: Development Server Process Management

**Before** starting any new development server (e.g., `npm start`, `npx expo start`, `webpack-dev-server`), **always** check for existing running processes that might conflict with the desired port (e.g., using `lsof -i:<PORT>` on Unix-like systems or equivalent tools).

  * **Only launch a new server** if no existing process is found or if the existing process has been gracefully terminated.
  * This prevents port conflicts and ensures development session continuity across different projects or re-starts.

-----

### Rule 3: Library & Documentation Context Acquisition

**Always** consult official documentation or a centralized knowledge base before integrating or utilizing new libraries or complex features.

  * **Focus documentation requests** on specific topics relevant to the current task (e.g., 'hooks', 'routing', 'components', 'styling', 'animations', 'state management').
  * **Be mindful of information overload**; aim for concise and relevant documentation retrieval.
  * Use library documentation to understand proper usage patterns, best practices, and potential pitfalls before implementing features with external libraries (e.g., React Router, TanStack Query, UI component libraries like Gluestack UI or Material-UI, animation libraries like `react-native-reanimated` or Framer Motion).

-----

### Rule 4: Structured Planning & Implementation

**Always** adopt a structured approach for all code planning, analysis, and implementation tasks. If you use an AI assistant like "Serena MCP," integrate its tools into this process.

  * **Begin each development session** with a clear understanding of the task objectives.
  * **Before making any code changes, reflect** on whether your proposed changes align with project goals, existing architecture, and design specifications.
  * **Document important context, progress updates, and key implementation decisions.** This can be in a dedicated README, a developer log, or specific memory features of an AI tool.
  * **Utilize IDE features and refactoring tools** for precise code modifications over manual file editing where possible.
  * **Review your work against the initial task definition** before marking it as complete.

-----

### Rule 5: Code Quality & Type Safety Assurance

**Always** run TypeScript compilation checks (`npx tsc --noEmit --skipLibCheck` or your project's equivalent) after making any significant code changes to React components or modules.

  * **Fix all TypeScript errors**, type compatibility issues, missing property definitions, and import/export problems *before* proceeding to the next steps.
  * Ensure all React components have proper TypeScript interfaces for their props and state, and use appropriate export methods.
  * Verify that component props match their expected TypeScript definitions from relevant UI libraries.
  * **Continuously monitor development server outputs** (e.g., console logs, Metro bundler warnings, Webpack build errors, browser console).
  * **Immediately address** any runtime errors, bundler warnings, or framework-specific errors that appear in development logs.

-----

### Rule 6: UI/UX Design Consistency & Component Alignment

**Always** reference established design systems, style guides, and mockups for visual fidelity targets (e.g., design tokens, color palettes, typography, spacing guidelines).

  * **Prioritize reusing pre-defined design tokens** (e.g., `primary-500`, `secondary-500`, specific `font-sizes`, `spacing-md`).
  * **Leverage and reuse optimized UI components** from a shared component library (e.g., `Button`, `Text`, `Box`, `VStack`, `HStack`).
  * **Adhere to a defined component alignment strategy** for consistent usage patterns and visual hierarchy.
  * **Prefer modern styling solutions** (e.g., Tailwind CSS, styled-components, CSS Modules) over inline styles or `StyleSheet.create` for dynamic styles, using `StyleSheet.create` for static, performance-critical styles when working with React Native.
  * **Verify UI implementation alignment** VERIFY UI implementation alignment by actively inspecting elements, styles, and responsiveness using #browser-tools-mcp Debugger Mode.

-----

### Rule 7: Development Environment Monitoring & Control

**Maintain awareness of your development environment's status.**

  * **Track bundle compilation status, hot module reloading, and component rendering errors.**
  * **Respond promptly** to TypeScript compilation errors, missing dependency warnings, and platform-specific issues.
  * **Be prepared to manage processes effectively** (e.g., killing conflicting processes when necessary, restarting servers for fresh builds).

-----

### Rule 8: Animation Implementation Standards

**Always** use performant animation libraries specific to your platform (e.g., `react-native-reanimated` for React Native, Framer Motion for Web) instead of basic or less optimized APIs.

  * **Implement animations efficiently**, utilizing concepts like shared values, animated styles, and timing/spring functions.
  * **Optimize animations for 60fps performance** by using native driver capabilities where available and avoiding unnecessary re-renders.
  * Reference existing animation patterns within your project to maintain consistency.

-----

### Rule 9: Debugging & Best Practices

**WHEN** developing for the web platform, **ALWAYS** use the `#browser-tools-mcp` to ensure UI quality and code health.

* **DEBUG web applications** by activating `Debugger Mode` in the following sequence:
    1.  Inspect relevant elements to identify the associated JavaScript files.
    2.  Set breakpoints at critical functions or state changes.
    3.  Step through code execution to trace logic and identify issues.
    4.  Monitor console output for errors and warnings.
* **CHECK best practices** regularly using `Audit Mode` (e.g., Lighthouse audits) to identify performance, accessibility, SEO, and progressive web app (PWA) improvements.

-----

### Rule 10: Performance Optimization

**Prioritize application performance across both web and app.**

  * **Minimize excessive `useState` and `useEffect` usage**; prefer React Context, `useReducer`, or dedicated state management libraries for complex state.
  * **Optimize asset loading** (e.g., using WebP format, explicit image dimensions, lazy loading with appropriate libraries).
  * **Implement code splitting and lazy loading** for non-critical components or routes using React's `Suspense` and dynamic imports.
  * **Profile performance** using browser developer tools, React Dev Tools, and platform-specific debugging features (e.g., Flipper for React Native).
  * **Prevent unnecessary re-renders** by memoizing components with `React.memo` and using `useMemo`/`useCallback` appropriately.
  * **Use `startTransition`/`useTransition`** for non-urgent state updates that don't block user interactions.
  * **Leverage robust data fetching and caching libraries** (e.g., TanStack Query) for efficient data management and background updates.

-----

### Rule 11: State Management Architecture

**Define and declare all application state using a schema validation library** (e.g., Zod) for compile-time and runtime type safety.

```typescript
const CameraStateSchema = z.object({
  activeTab: z.enum(['camera', 'upload', 'gallery']),
  facing: z.enum(['back', 'front']),
  flash: z.enum(['off', 'on']),
  isCapturing: z.boolean(),
  hasPermission: z.boolean().nullable(),
});
type ICameraState = z.infer<typeof CameraStateSchema>;
// Example usage:
const [hasPermission, setHasPermission] = useState<ICameraState['hasPermission']>(null);
```

  * **Choose appropriate state management patterns** based on scope:
      * **React Context API** for managing global application state (user preferences, theme, authentication).
      * **React Suspense + `use` hook pattern** for declarative loading state presentation.
      * **Lightweight, TypeScript-friendly libraries** like Zustand for complex local component state or shared module state.
      * **URL parameters and routing state** using libraries like React Router or `expo-router`.
  * **Maintain state immutability** and avoid direct mutations to prevent unexpected side effects and simplify debugging.

-----

### Rule 12: Validation & Robust Error Handling

**Use a schema validation library** (e.g., Zod) for runtime type validation, API response validation, and form input validation.

  * **Prioritize comprehensive error handling and edge case management:**
      * Handle error conditions at the beginning of functions using early returns.
      * Use the early return pattern to avoid deeply nested conditional statements.
      * Avoid unnecessary `else` statements; prefer the `if-return` pattern for cleaner code flow.
  * **Implement global error boundaries** using React Error Boundary components to catch unexpected errors in the UI tree.
  * **Provide user-friendly error messages and recovery options** to the end-user.
  * **Log errors appropriately for debugging** while protecting user privacy and sensitive information.

-----