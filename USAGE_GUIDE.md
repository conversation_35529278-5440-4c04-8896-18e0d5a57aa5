# Apple App Store Connect API - Usage Guide

## Quick Start

### 1. Get Your API Credentials

1. **Log into App Store Connect**
   - Go to [App Store Connect](https://appstoreconnect.apple.com)
   - Navigate to **Users and Access** → **Keys**

2. **Create API Key**
   - Click **Generate API Key** or the **+** button
   - Name: `Price Store API Key` (or any descriptive name)
   - Access: **Admin** or **Developer** (depending on your needs)
   - Roles: Ensure access to subscription management
   - Click **Generate**

3. **Download and Save**
   - **Download the .p8 file immediately** (only chance!)
   - Note the **Key ID** (10 characters, e.g., "ABC123DEFG")
   - Note the **Issuer ID** (UUID format)

### 2. Configure the Application

1. **Copy environment template**:
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` file**:
   ```env
   APPLE_KEY_ID=ABC123DEFG
   APPLE_ISSUER_ID=12345678-1234-1234-1234-123456789012
   APPLE_PRIVATE_KEY="-----<PERSON><PERSON>IN PRIVATE KEY-----
   MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQg...
   -----END PRIVATE KEY-----"
   ```

3. **Start the application**:
   ```bash
   pnpm dev
   ```

### 3. Using the Application

1. **Open** http://localhost:5173 in your browser
2. **Select a subscription** from the dropdown
3. **Choose territories** (optional) - leave empty for all countries
4. **Click "Fetch Price Points"**
5. **View results** organized by country/territory

## API Endpoints Reference

### Authentication
- **Method**: JWT (JSON Web Token) with ES256 algorithm
- **Token Expiry**: 20 minutes maximum
- **Headers**: `Authorization: Bearer <jwt_token>`

### Subscriptions Endpoint
```
GET https://api.appstoreconnect.apple.com/v1/subscriptions
```
**Query Parameters**:
- `limit`: Number of results (default: 50)
- `fields[subscriptions]`: Specific fields to return

### Price Points Endpoint
```
GET https://api.appstoreconnect.apple.com/v1/subscriptions/{id}/pricePoints
```
**Query Parameters**:
- `filter[territory]`: Array of territory IDs
- `fields[subscriptionPricePoints]`: Specific fields to return
- `fields[territories]`: Territory fields to include
- `include`: Related objects to include (territory, subscription)
- `limit`: Number of results (default: 200)

## Code Examples

### Basic Usage
```typescript
import { createServiceFromEnv } from './services/appStoreConnect';

// Create service instance
const service = createServiceFromEnv();

// Get all subscriptions
const subscriptions = await service.subscriptions.getSubscriptions();

// Get price points for a subscription
const pricePoints = await service.subscriptions.getSubscriptionPricePointsByCountry(
  'subscription-id',
  ['US', 'GB', 'CA'] // Optional: specific territories
);
```

### Manual Configuration
```typescript
import { AppStoreConnectAuth, AppStoreConnectClient, SubscriptionService } from './services/appStoreConnect';

const auth = new AppStoreConnectAuth({
  keyId: 'your-key-id',
  issuerId: 'your-issuer-id',
  privateKey: 'your-private-key'
});

const client = new AppStoreConnectClient(auth);
const subscriptions = new SubscriptionService(client);
```

### Error Handling
```typescript
import { AppStoreConnectAPIError, parseError } from './services/appStoreConnect';

try {
  const pricePoints = await service.subscriptions.getSubscriptionPricePoints('id');
} catch (error) {
  if (error instanceof AppStoreConnectAPIError) {
    console.error('API Error:', error.status, error.errors);
  } else {
    const errorInfo = parseError(error);
    console.error('Error:', errorInfo.title, errorInfo.message);
    
    if (errorInfo.retryable) {
      // Implement retry logic
    }
  }
}
```

## Territory Codes

Common territory codes supported:
- **US**: United States (USD)
- **GB**: United Kingdom (GBP)
- **CA**: Canada (CAD)
- **AU**: Australia (AUD)
- **DE**: Germany (EUR)
- **FR**: France (EUR)
- **JP**: Japan (JPY)
- **CN**: China (CNY)
- **IN**: India (INR)
- **BR**: Brazil (BRL)

## Response Format

### Subscription Object
```json
{
  "type": "subscriptions",
  "id": "subscription-id",
  "attributes": {
    "name": "Premium Subscription",
    "productId": "com.example.premium",
    "familySharable": true,
    "state": "APPROVED",
    "subscriptionPeriod": "ONE_MONTH"
  }
}
```

### Price Point Object
```json
{
  "type": "subscriptionPricePoints",
  "id": "price-point-id",
  "attributes": {
    "customerPrice": "$9.99",
    "proceeds": "$6.99",
    "proceedsDate": "2024-01-01"
  },
  "relationships": {
    "territory": {
      "data": { "type": "territories", "id": "USA" }
    }
  }
}
```

## Performance Tips

1. **Use Pagination**: Set appropriate `limit` values (recommended: 200)
2. **Filter Fields**: Only request needed fields using `fields[*]` parameters
3. **Cache Tokens**: JWT tokens are cached automatically for 20 minutes
4. **Filter Territories**: Specify territories to reduce response size
5. **Handle Rate Limits**: Implement exponential backoff for 429 errors

## Security Best Practices

1. **Environment Variables**: Never commit `.env` files
2. **Private Key Security**: Store private keys securely
3. **Token Expiry**: Tokens automatically expire after 20 minutes
4. **HTTPS Only**: All API calls use HTTPS
5. **Key Rotation**: Regularly rotate API keys
6. **Minimal Permissions**: Use least privilege principle for API keys

## Browser vs Server Considerations

This implementation uses the `jose` library for browser compatibility. For production:

1. **Recommended**: Move JWT generation to a backend server
2. **Alternative**: Use a proxy server for API calls
3. **Current**: Browser-based implementation (development/demo purposes)

The browser implementation is suitable for:
- Development and testing
- Internal tools
- Proof of concepts

For production applications with sensitive data, consider server-side implementation.
